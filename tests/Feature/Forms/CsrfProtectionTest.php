<?php

declare(strict_types=1);

use App\Models\User;

describe('CSRF Protection Tests', function () {
    beforeEach(function () {
        $this->user = createVerifiedUser();
        $this->admin = createAdminUser();
        $this->category = createTestCategory();
        $this->auction = createActiveAuction([
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
        ]);
    });

    describe('Form CSRF Token Validation', function () {
        test('auction creation requires CSRF token', function () {
            $auctionData = generateTestAuctionData([
                'category_id' => $this->category->id,
            ]);

            // Request without CSRF token
            $response = $this->actingAs($this->user)
                ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
                ->post('/auctions', $auctionData);

            expect($response->status())->toBe(419);
        });

        test('bid placement requires CSRF token', function () {
            $bidData = generateTestBidData($this->auction->id);

            $response = $this->actingAs($this->user)
                ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
                ->post('/bids', $bidData);

            expect($response->status())->toBe(419);
        });

        test('profile update requires CSRF token', function () {
            $profileData = ['name' => 'Updated Name'];

            $response = $this->actingAs($this->user)
                ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
                ->patch('/settings/profile', $profileData);

            expect($response->status())->toBe(419);
        });

        test('password change requires CSRF token', function () {
            $passwordData = [
                'current_password' => 'password',
                'password' => 'newpassword123',
                'password_confirmation' => 'newpassword123',
            ];

            $response = $this->actingAs($this->user)
                ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
                ->put('/settings/password', $passwordData);

            expect($response->status())->toBe(419);
        });

        test('auction deletion requires CSRF token', function () {
            $response = $this->actingAs($this->user)
                ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
                ->delete("/auctions/{$this->auction->id}");

            expect($response->status())->toBe(419);
        });
    });

    describe('CSRF Token Generation and Validation', function () {
        test('forms include valid CSRF tokens', function () {
            $response = $this->actingAs($this->user)->get('/auctions/create');
            
            assertInertiaResponse($response, 'Auctions/Create');
            
            // Check that CSRF token is provided in props
            $props = $response->json('props');
            expect($props)->toHaveKey('csrf_token');
            expect($props['csrf_token'])->toBeString();
            expect(strlen($props['csrf_token']))->toBeGreaterThan(10);
        });

        test('CSRF tokens are unique per session', function () {
            // Get first token
            $response1 = $this->actingAs($this->user)->get('/auctions/create');
            $token1 = $response1->json('props.csrf_token');

            // Start new session
            session()->flush();
            session()->regenerate();

            // Get second token
            $response2 = $this->actingAs($this->user)->get('/auctions/create');
            $token2 = $response2->json('props.csrf_token');

            expect($token1)->not->toBe($token2);
        });

        test('CSRF tokens expire appropriately', function () {
            // This would test token expiration if implemented
            $this->markTestSkipped('CSRF token expiration testing requires specific configuration');
        });

        test('invalid CSRF tokens are rejected', function () {
            $auctionData = generateTestAuctionData([
                'category_id' => $this->category->id,
                '_token' => 'invalid-token',
            ]);

            $response = $this->actingAs($this->user)->post('/auctions', $auctionData);

            expect($response->status())->toBe(419);
        });

        test('expired CSRF tokens are rejected', function () {
            // This would test expired token rejection if implemented
            $this->markTestSkipped('Expired CSRF token testing requires specific implementation');
        });
    });

    describe('CSRF Protection for Different HTTP Methods', function () {
        test('POST requests require CSRF protection', function () {
            $routes = [
                ['/auctions', generateTestAuctionData(['category_id' => $this->category->id])],
                ['/bids', generateTestBidData($this->auction->id)],
                ['/logout', []],
            ];

            foreach ($routes as [$route, $data]) {
                $response = $this->actingAs($this->user)
                    ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
                    ->post($route, $data);

                expect($response->status())->toBe(419);
            }
        });

        test('PUT requests require CSRF protection', function () {
            $routes = [
                ["/auctions/{$this->auction->id}", ['title' => 'Updated Title']],
                ['/settings/password', [
                    'current_password' => 'password',
                    'password' => 'new',
                    'password_confirmation' => 'new'
                ]],
            ];

            foreach ($routes as [$route, $data]) {
                $response = $this->actingAs($this->user)
                    ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
                    ->put($route, $data);

                expect($response->status())->toBe(419);
            }
        });

        test('PATCH requests require CSRF protection', function () {
            $response = $this->actingAs($this->user)
                ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
                ->patch('/settings/profile', ['name' => 'Updated']);

            expect($response->status())->toBe(419);
        });

        test('DELETE requests require CSRF protection', function () {
            $routes = [
                "/auctions/{$this->auction->id}",
                '/settings/profile',
            ];

            foreach ($routes as $route) {
                $response = $this->actingAs($this->user)
                    ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
                    ->delete($route);

                expect($response->status())->toBe(419);
            }
        });

        test('GET requests do not require CSRF protection', function () {
            $routes = [
                '/',
                '/auctions',
                '/auctions/create',
                '/settings/profile',
            ];

            foreach ($routes as $route) {
                $response = $this->actingAs($this->user)->get($route);
                expect($response->status())->not->toBe(419);
            }
        });

        test('HEAD requests do not require CSRF protection', function () {
            $routes = [
                '/',
                '/auctions',
            ];

            foreach ($routes as $route) {
                $response = $this->head($route);
                expect($response->status())->not->toBe(419);
            }
        });
    });

    describe('CSRF Protection for AJAX Requests', function () {
        test('AJAX POST requests require CSRF token', function () {
            $bidData = generateTestBidData($this->auction->id);

            $response = $this->actingAs($this->user)
                ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
                ->postJson('/bids', $bidData);

            expect($response->status())->toBe(419);
        });

        test('AJAX requests with valid CSRF token succeed', function () {
            $bidData = generateTestBidData($this->auction->id, [
                '_token' => csrf_token(),
            ]);

            $response = $this->actingAs($this->user)->postJson('/bids', $bidData);

            expect($response->status())->not->toBe(419);
        });

        test('CSRF token can be sent in header', function () {
            $bidData = generateTestBidData($this->auction->id);

            $response = $this->actingAs($this->user)
                ->withHeaders(['X-CSRF-TOKEN' => csrf_token()])
                ->postJson('/bids', $bidData);

            expect($response->status())->not->toBe(419);
        });

        test('CSRF token can be sent as X-XSRF-TOKEN', function () {
            $bidData = generateTestBidData($this->auction->id);

            $response = $this->actingAs($this->user)
                ->withHeaders(['X-XSRF-TOKEN' => csrf_token()])
                ->postJson('/bids', $bidData);

            expect($response->status())->not->toBe(419);
        });
    });

    describe('CSRF Protection Exceptions', function () {
        test('API routes may be exempt from CSRF protection', function () {
            // API routes typically use token authentication instead
            $response = $this->withHeaders([
                'Authorization' => 'Bearer valid-api-token',
            ])->postJson('/api/v1/bids', generateTestBidData($this->auction->id));

            // Should not return 419 (CSRF error)
            expect($response->status())->not->toBe(419);
        });

        test('webhook routes are exempt from CSRF protection', function () {
            $webhookData = [
                'event' => 'payment.completed',
                'data' => ['payment_id' => '12345'],
            ];

            $response = $this->postJson('/api/payments/webhook', $webhookData);

            expect($response->status())->not->toBe(419);
        });

        test('health check routes are exempt from CSRF protection', function () {
            $response = $this->get('/up');
            expect($response->status())->not->toBe(419);
        });
    });

    describe('CSRF Error Handling', function () {
        test('CSRF errors return appropriate status code', function () {
            $response = $this->actingAs($this->user)
                ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
                ->post('/auctions', []);

            expect($response->status())->toBe(419);
        });

        test('CSRF errors return JSON for AJAX requests', function () {
            $response = $this->actingAs($this->user)
                ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
                ->postJson('/bids', []);

            expect($response->status())->toBe(419);
            expect($response->json())->toHaveKey('message');
        });

        test('CSRF errors redirect for regular form submissions', function () {
            $response = $this->actingAs($this->user)
                ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
                ->post('/auctions', []);

            expect($response->status())->toBe(419);
            // Should include appropriate error message
        });

        test('CSRF errors preserve form data when possible', function () {
            // This would test that form data is preserved on CSRF errors
            $this->markTestSkipped('CSRF error form data preservation testing requires specific implementation');
        });
    });

    describe('CSRF Protection Performance', function () {
        test('CSRF validation does not significantly impact performance', function () {
            $auctionData = generateTestAuctionData([
                'category_id' => $this->category->id,
                '_token' => csrf_token(),
            ]);

            assertExecutionTimeUnder(function() use ($auctionData) {
                $this->actingAs($this->user)->post('/auctions', $auctionData);
            }, 2.0);
        });

        test('CSRF token generation is efficient', function () {
            assertExecutionTimeUnder(function() {
                for ($i = 0; $i < 100; $i++) {
                    csrf_token();
                }
            }, 1.0);
        });
    });
});
