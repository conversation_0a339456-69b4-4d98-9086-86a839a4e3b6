<?php

declare(strict_types=1);

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;

describe('Breadcrumb Navigation Tests', function () {
    beforeEach(function () {
        $this->user = createVerifiedUser();
        $this->admin = createAdminUser();
        $this->category = createTestCategory([
            'name' => 'Electronics',
            'slug' => 'electronics',
        ]);
        $this->auction = createActiveAuction([
            'title' => 'Test Auction',
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
        ]);
    });

    describe('Public Page Breadcrumbs', function () {
        test('home page has no breadcrumbs', function () {
            $response = $this->get('/');
            assertInertiaResponse($response, 'Home');
            
            $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
            expect($breadcrumbs)->toBeEmpty();
        });

        test('auction listing page has correct breadcrumbs', function () {
            $response = $this->get('/auctions');
            assertInertiaResponse($response, 'Auctions/Index');
            
            $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
            expect($breadcrumbs)->toHaveCount(1);
            expect($breadcrumbs[0]['title'])->toBe('Auctions');
            expect($breadcrumbs[0]['href'])->toBe('/auctions');
        });

        test('featured auctions page has correct breadcrumbs', function () {
            $response = $this->get('/auctions/featured');
            assertInertiaResponse($response, 'Auctions/Featured');
            
            $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
            expect($breadcrumbs)->toHaveCount(2);
            expect($breadcrumbs[0]['title'])->toBe('Auctions');
            expect($breadcrumbs[1]['title'])->toBe('Featured');
        });

        test('auction detail page has correct breadcrumbs', function () {
            $response = $this->get("/auctions/{$this->auction->id}");
            assertInertiaResponse($response, 'Auctions/Show');
            
            $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
            expect($breadcrumbs)->toHaveCount(2);
            expect($breadcrumbs[0]['title'])->toBe('Auctions');
            expect($breadcrumbs[1]['title'])->toBe($this->auction->title);
        });

        test('category listing page has correct breadcrumbs', function () {
            $response = $this->get('/categories');
            assertInertiaResponse($response, 'Categories/Index');
            
            $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
            expect($breadcrumbs)->toHaveCount(1);
            expect($breadcrumbs[0]['title'])->toBe('Categories');
        });

        test('category detail page has correct breadcrumbs', function () {
            $response = $this->get("/categories/{$this->category->slug}");
            assertInertiaResponse($response, 'Categories/Show');
            
            $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
            expect($breadcrumbs)->toHaveCount(2);
            expect($breadcrumbs[0]['title'])->toBe('Categories');
            expect($breadcrumbs[1]['title'])->toBe($this->category->name);
        });

        test('category auctions page has correct breadcrumbs', function () {
            $response = $this->get("/categories/{$this->category->slug}/auctions");
            assertInertiaResponse($response, 'Categories/Auctions');
            
            $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
            expect($breadcrumbs)->toHaveCount(3);
            expect($breadcrumbs[0]['title'])->toBe('Categories');
            expect($breadcrumbs[1]['title'])->toBe($this->category->name);
            expect($breadcrumbs[2]['title'])->toBe('Auctions');
        });
    });

    describe('Authenticated User Breadcrumbs', function () {
        test('dashboard has correct breadcrumbs', function () {
            $response = $this->actingAs($this->user)->get('/dashboard');
            assertInertiaResponse($response, 'Dashboard');
            
            $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
            expect($breadcrumbs)->toHaveCount(1);
            expect($breadcrumbs[0]['title'])->toBe('Dashboard');
        });

        test('auction creation page has correct breadcrumbs', function () {
            $response = $this->actingAs($this->user)->get('/auctions/create');
            assertInertiaResponse($response, 'Auctions/Create');
            
            $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
            expect($breadcrumbs)->toHaveCount(2);
            expect($breadcrumbs[0]['title'])->toBe('Auctions');
            expect($breadcrumbs[1]['title'])->toBe('Create');
        });

        test('auction edit page has correct breadcrumbs', function () {
            $response = $this->actingAs($this->user)->get("/auctions/{$this->auction->id}/edit");
            
            if ($response->status() === 200) {
                $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
                expect($breadcrumbs)->toHaveCount(3);
                expect($breadcrumbs[0]['title'])->toBe('Auctions');
                expect($breadcrumbs[1]['title'])->toBe($this->auction->title);
                expect($breadcrumbs[2]['title'])->toBe('Edit');
            }
        });

        test('settings pages have correct breadcrumbs', function () {
            $settingsPages = [
                '/settings/profile' => 'Profile',
                '/settings/password' => 'Password',
                '/settings/appearance' => 'Appearance',
            ];

            foreach ($settingsPages as $route => $pageTitle) {
                $response = $this->actingAs($this->user)->get($route);
                
                $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
                expect($breadcrumbs)->toHaveCount(2);
                expect($breadcrumbs[0]['title'])->toBe('Settings');
                expect($breadcrumbs[1]['title'])->toBe($pageTitle);
            }
        });
    });

    describe('Admin Panel Breadcrumbs', function () {
        test('admin dashboard has correct breadcrumbs', function () {
            $response = $this->actingAs($this->admin)->get('/admin');
            
            if ($response->status() === 200) {
                $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
                expect($breadcrumbs)->toHaveCount(1);
                expect($breadcrumbs[0]['title'])->toBe('Admin');
            }
        });

        test('admin management pages have correct breadcrumbs', function () {
            $adminPages = [
                '/admin/users' => 'Users',
                '/admin/auctions' => 'Auctions',
                '/admin/settings' => 'Settings',
            ];

            foreach ($adminPages as $route => $pageTitle) {
                $response = $this->actingAs($this->admin)->get($route);
                
                if ($response->status() === 200) {
                    $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
                    expect($breadcrumbs)->toHaveCount(2);
                    expect($breadcrumbs[0]['title'])->toBe('Admin');
                    expect($breadcrumbs[1]['title'])->toBe($pageTitle);
                }
            }
        });

        test('admin user detail page has correct breadcrumbs', function () {
            $response = $this->actingAs($this->admin)->get("/admin/users/{$this->user->id}");
            
            if ($response->status() === 200) {
                $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
                expect($breadcrumbs)->toHaveCount(3);
                expect($breadcrumbs[0]['title'])->toBe('Admin');
                expect($breadcrumbs[1]['title'])->toBe('Users');
                expect($breadcrumbs[2]['title'])->toBe($this->user->name);
            }
        });

        test('admin auction detail page has correct breadcrumbs', function () {
            $response = $this->actingAs($this->admin)->get("/admin/auctions/{$this->auction->id}");
            
            if ($response->status() === 200) {
                $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
                expect($breadcrumbs)->toHaveCount(3);
                expect($breadcrumbs[0]['title'])->toBe('Admin');
                expect($breadcrumbs[1]['title'])->toBe('Auctions');
                expect($breadcrumbs[2]['title'])->toBe($this->auction->title);
            }
        });
    });

    describe('Breadcrumb Structure and Functionality', function () {
        test('breadcrumbs have required properties', function () {
            $response = $this->get('/auctions');
            $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
            
            foreach ($breadcrumbs as $breadcrumb) {
                expect($breadcrumb)->toHaveKey('title');
                expect($breadcrumb)->toHaveKey('href');
                expect($breadcrumb['title'])->toBeString();
                expect($breadcrumb['href'])->toBeString();
            }
        });

        test('breadcrumb links are valid URLs', function () {
            $response = $this->get("/categories/{$this->category->slug}/auctions");
            $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
            
            foreach ($breadcrumbs as $breadcrumb) {
                if (isset($breadcrumb['href'])) {
                    $linkResponse = $this->get($breadcrumb['href']);
                    expect($linkResponse->status())->toBeIn([200, 302]);
                }
            }
        });

        test('breadcrumbs maintain hierarchy', function () {
            $response = $this->get("/categories/{$this->category->slug}/auctions");
            $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
            
            // Should go from general to specific
            expect($breadcrumbs[0]['title'])->toBe('Categories');
            expect($breadcrumbs[1]['title'])->toBe($this->category->name);
            expect($breadcrumbs[2]['title'])->toBe('Auctions');
        });

        test('breadcrumbs are consistent across similar pages', function () {
            // All auction-related pages should start with "Auctions"
            $auctionPages = [
                '/auctions',
                '/auctions/featured',
                '/auctions/ending-soon',
            ];

            foreach ($auctionPages as $page) {
                $response = $this->get($page);
                $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
                
                if (!empty($breadcrumbs)) {
                    expect($breadcrumbs[0]['title'])->toBe('Auctions');
                }
            }
        });

        test('breadcrumbs handle special characters correctly', function () {
            $specialCategory = createTestCategory([
                'name' => 'Art & Collectibles',
                'slug' => 'art-collectibles',
            ]);

            $response = $this->get("/categories/{$specialCategory->slug}");
            $breadcrumbs = $response->json('props.breadcrumbs') ?? [];
            
            expect($breadcrumbs[1]['title'])->toBe('Art & Collectibles');
        });
    });

    describe('Breadcrumb Performance', function () {
        test('breadcrumb generation does not impact page load time', function () {
            assertExecutionTimeUnder(function() {
                $this->get("/categories/{$this->category->slug}/auctions");
            }, 2.0);
        });

        test('breadcrumb queries are optimized', function () {
            testDatabaseQueryPerformance(function() {
                $this->get("/categories/{$this->category->slug}/auctions");
            }, 10); // Should not require excessive queries for breadcrumbs
        });
    });
});
