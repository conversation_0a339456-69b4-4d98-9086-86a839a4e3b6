<?php

declare(strict_types=1);

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

describe('File Upload Tests', function () {
    beforeEach(function () {
        $this->user = createVerifiedUser();
        $this->admin = createAdminUser();
        $this->category = createTestCategory();
        $this->auction = createActiveAuction([
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
        ]);
        
        Storage::fake('public');
    });

    describe('Auction Image Upload', function () {
        test('user can upload valid image files', function () {
            $validImageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

            foreach ($validImageTypes as $type) {
                $file = UploadedFile::fake()->image("test.{$type}", 800, 600);

                $response = $this->actingAs($this->user)
                    ->post("/auctions/{$this->auction->id}/images", [
                        'images' => [$file],
                    ]);

                expect($response->status())->toBeIn([200, 201, 302]);
                Storage::disk('public')->assertExists("auctions/{$this->auction->id}/" . $file->hashName());
            }
        });

        test('user cannot upload invalid file types', function () {
            $invalidFiles = [
                UploadedFile::fake()->create('document.pdf', 1000),
                UploadedFile::fake()->create('script.js', 500),
                UploadedFile::fake()->create('executable.exe', 2000),
                UploadedFile::fake()->create('archive.zip', 1500),
            ];

            foreach ($invalidFiles as $file) {
                $response = $this->actingAs($this->user)
                    ->post("/auctions/{$this->auction->id}/images", [
                        'images' => [$file],
                    ]);

                assertValidationErrors($response, ['images.0']);
            }
        });

        test('user cannot upload files exceeding size limit', function () {
            // Create a large file (assuming 5MB limit)
            $largeFile = UploadedFile::fake()->image('large.jpg')->size(6000); // 6MB

            $response = $this->actingAs($this->user)
                ->post("/auctions/{$this->auction->id}/images", [
                    'images' => [$largeFile],
                ]);

            assertValidationErrors($response, ['images.0']);
        });

        test('user cannot upload too many images', function () {
            // Assuming max 10 images per auction
            $images = [];
            for ($i = 0; $i < 12; $i++) {
                $images[] = UploadedFile::fake()->image("image{$i}.jpg");
            }

            $response = $this->actingAs($this->user)
                ->post("/auctions/{$this->auction->id}/images", [
                    'images' => $images,
                ]);

            assertValidationErrors($response, ['images']);
        });

        test('uploaded images are properly processed', function () {
            $file = UploadedFile::fake()->image('test.jpg', 1200, 800);

            $response = $this->actingAs($this->user)
                ->post("/auctions/{$this->auction->id}/images", [
                    'images' => [$file],
                ]);

            expect($response->status())->toBeIn([200, 201, 302]);

            // Check that thumbnails are created
            $storedFiles = Storage::disk('public')->files("auctions/{$this->auction->id}");
            expect(count($storedFiles))->toBeGreaterThan(1); // Original + thumbnail(s)
        });

        test('only auction owner can upload images', function () {
            $otherUser = createVerifiedUser();
            $file = UploadedFile::fake()->image('test.jpg');

            $response = $this->actingAs($otherUser)
                ->post("/auctions/{$this->auction->id}/images", [
                    'images' => [$file],
                ]);

            expect($response->status())->toBeIn([403, 404]);
        });

        test('admin can upload images to any auction', function () {
            $file = UploadedFile::fake()->image('admin-test.jpg');

            $response = $this->actingAs($this->admin)
                ->post("/auctions/{$this->auction->id}/images", [
                    'images' => [$file],
                ]);

            expect($response->status())->toBeIn([200, 201, 302]);
        });

        test('image upload validates file integrity', function () {
            // Create a fake image file that's actually text
            $fakeImage = UploadedFile::fake()->createWithContent(
                'fake.jpg',
                'This is not an image file'
            );

            $response = $this->actingAs($this->user)
                ->post("/auctions/{$this->auction->id}/images", [
                    'images' => [$fakeImage],
                ]);

            assertValidationErrors($response, ['images.0']);
        });
    });

    describe('Profile Avatar Upload', function () {
        test('user can upload profile avatar', function () {
            $avatar = UploadedFile::fake()->image('avatar.jpg', 300, 300);

            $response = $this->actingAs($this->user)
                ->post('/settings/avatar', [
                    'avatar' => $avatar,
                ]);

            if ($response->status() === 200 || $response->status() === 302) {
                Storage::disk('public')->assertExists('avatars/' . $avatar->hashName());
            }
        });

        test('avatar upload validates image dimensions', function () {
            $tooSmall = UploadedFile::fake()->image('small.jpg', 50, 50);
            $tooLarge = UploadedFile::fake()->image('large.jpg', 5000, 5000);

            foreach ([$tooSmall, $tooLarge] as $file) {
                $response = $this->actingAs($this->user)
                    ->post('/settings/avatar', [
                        'avatar' => $file,
                    ]);

                if ($response->status() === 422) {
                    assertValidationErrors($response, ['avatar']);
                }
            }
        });

        test('avatar upload replaces existing avatar', function () {
            // Upload first avatar
            $firstAvatar = UploadedFile::fake()->image('first.jpg');
            $this->actingAs($this->user)->post('/settings/avatar', [
                'avatar' => $firstAvatar,
            ]);

            $firstPath = 'avatars/' . $firstAvatar->hashName();

            // Upload second avatar
            $secondAvatar = UploadedFile::fake()->image('second.jpg');
            $response = $this->actingAs($this->user)->post('/settings/avatar', [
                'avatar' => $secondAvatar,
            ]);

            if ($response->status() === 200 || $response->status() === 302) {
                // Old avatar should be deleted
                Storage::disk('public')->assertMissing($firstPath);
                // New avatar should exist
                Storage::disk('public')->assertExists('avatars/' . $secondAvatar->hashName());
            }
        });
    });

    describe('Document Upload Security', function () {
        test('file uploads prevent path traversal', function () {
            $maliciousFile = UploadedFile::fake()->createWithContent(
                '../../../malicious.php',
                '<?php echo "hacked"; ?>'
            );

            $response = $this->actingAs($this->user)
                ->post("/auctions/{$this->auction->id}/images", [
                    'images' => [$maliciousFile],
                ]);

            // Should reject the file or sanitize the filename
            expect($response->status())->toBeIn([422, 400]);
        });

        test('file uploads prevent executable uploads', function () {
            $executableFiles = [
                UploadedFile::fake()->createWithContent('script.php', '<?php phpinfo(); ?>'),
                UploadedFile::fake()->createWithContent('script.js', 'alert("xss");'),
                UploadedFile::fake()->create('malware.exe', 1000),
            ];

            foreach ($executableFiles as $file) {
                $response = $this->actingAs($this->user)
                    ->post("/auctions/{$this->auction->id}/images", [
                        'images' => [$file],
                    ]);

                assertValidationErrors($response, ['images.0']);
            }
        });

        test('file uploads scan for malware', function () {
            // This would test virus scanning if implemented
            $this->markTestSkipped('Malware scanning testing requires specific implementation');
        });

        test('uploaded files are stored securely', function () {
            $file = UploadedFile::fake()->image('secure-test.jpg');

            $response = $this->actingAs($this->user)
                ->post("/auctions/{$this->auction->id}/images", [
                    'images' => [$file],
                ]);

            if ($response->status() === 200 || $response->status() === 302) {
                // File should be stored with hashed name
                $storedFiles = Storage::disk('public')->files("auctions/{$this->auction->id}");
                expect($storedFiles[0])->not->toContain('secure-test.jpg');
            }
        });
    });

    describe('File Upload Performance', function () {
        test('multiple file uploads are processed efficiently', function () {
            $files = [];
            for ($i = 0; $i < 5; $i++) {
                $files[] = UploadedFile::fake()->image("image{$i}.jpg", 800, 600);
            }

            assertExecutionTimeUnder(function() use ($files) {
                $this->actingAs($this->user)
                    ->post("/auctions/{$this->auction->id}/images", [
                        'images' => $files,
                    ]);
            }, 10.0); // Should complete within 10 seconds
        });

        test('large file uploads handle timeouts gracefully', function () {
            $largeFile = UploadedFile::fake()->image('large.jpg', 2000, 2000)->size(4000); // 4MB

            $response = $this->actingAs($this->user)
                ->post("/auctions/{$this->auction->id}/images", [
                    'images' => [$largeFile],
                ]);

            // Should either succeed or fail gracefully
            expect($response->status())->toBeIn([200, 201, 302, 413, 422]);
        });
    });

    describe('File Upload Cleanup', function () {
        test('failed uploads are cleaned up', function () {
            $file = UploadedFile::fake()->create('invalid.txt', 1000);

            $response = $this->actingAs($this->user)
                ->post("/auctions/{$this->auction->id}/images", [
                    'images' => [$file],
                ]);

            // Failed upload should not leave files on disk
            $storedFiles = Storage::disk('public')->files("auctions/{$this->auction->id}");
            expect($storedFiles)->toBeEmpty();
        });

        test('orphaned files are identified for cleanup', function () {
            // This would test cleanup of files not associated with any records
            $this->markTestSkipped('Orphaned file cleanup testing requires specific implementation');
        });

        test('deleted auctions clean up associated files', function () {
            $file = UploadedFile::fake()->image('test.jpg');

            $this->actingAs($this->user)
                ->post("/auctions/{$this->auction->id}/images", [
                    'images' => [$file],
                ]);

            // Delete the auction
            $this->actingAs($this->user)->delete("/auctions/{$this->auction->id}");

            // Files should be cleaned up
            $storedFiles = Storage::disk('public')->files("auctions/{$this->auction->id}");
            expect($storedFiles)->toBeEmpty();
        });
    });

    describe('File Access Control', function () {
        test('uploaded files have correct permissions', function () {
            $file = UploadedFile::fake()->image('permission-test.jpg');

            $response = $this->actingAs($this->user)
                ->post("/auctions/{$this->auction->id}/images", [
                    'images' => [$file],
                ]);

            if ($response->status() === 200 || $response->status() === 302) {
                // Files should be publicly accessible for auction images
                $storedFiles = Storage::disk('public')->files("auctions/{$this->auction->id}");
                $fileUrl = Storage::disk('public')->url($storedFiles[0]);
                
                $fileResponse = $this->get($fileUrl);
                expect($fileResponse->status())->toBe(200);
            }
        });

        test('private files require authentication', function () {
            // This would test private file access if implemented
            $this->markTestSkipped('Private file access testing requires specific implementation');
        });
    });
});
