<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Bind repository interfaces to their implementations
        $this->app->bind(
            \App\Domain\Auction\Repositories\AuctionRepositoryInterface::class,
            \App\Infrastructure\Repositories\EloquentAuctionRepository::class
        );

        $this->app->bind(
            \App\Domain\Auction\Repositories\CategoryRepositoryInterface::class,
            \App\Infrastructure\Repositories\EloquentCategoryRepository::class
        );

        $this->app->bind(
            \App\Domain\Auction\Repositories\BidRepositoryInterface::class,
            \App\Infrastructure\Repositories\EloquentBidRepository::class
        );
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
