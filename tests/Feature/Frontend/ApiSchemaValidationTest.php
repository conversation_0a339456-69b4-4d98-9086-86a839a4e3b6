<?php

declare(strict_types=1);

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;

describe('API Response Schema Validation Tests', function () {
    beforeEach(function () {
        $this->user = createVerifiedUser();
        $this->admin = createAdminUser();
        $this->category = createTestCategory();
        $this->auction = createActiveAuction([
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
        ]);
    });

    describe('Public API Schema Validation', function () {
        test('auction list API returns complete schema', function () {
            $response = $this->getJson('/api/auctions');
            assertJsonApiResponse($response);
            
            $data = $response->json('data');
            foreach ($data as $auction) {
                // Required fields that frontend expects
                expect($auction)->toHaveKey('id');
                expect($auction)->toHaveKey('title');
                expect($auction)->toHaveKey('description');
                expect($auction)->toHaveKey('starting_bid');
                expect($auction)->toHaveKey('current_bid');
                expect($auction)->toHaveKey('end_time');
                expect($auction)->toHaveKey('status');
                expect($auction)->toHaveKey('images');
                expect($auction)->toHaveKey('category');
                expect($auction)->toHaveKey('user');
                
                // Validate nested user object
                expect($auction['user'])->toHaveKey('id');
                expect($auction['user'])->toHaveKey('name');
                expect($auction['user'])->toHaveKey('avatar');
                
                // Validate images array
                expect($auction['images'])->toBeArray();
                
                // Validate category object
                expect($auction['category'])->toHaveKey('id');
                expect($auction['category'])->toHaveKey('name');
                expect($auction['category'])->toHaveKey('slug');
            }
        });

        test('auction detail API returns complete schema', function () {
            $response = $this->getJson("/api/auctions/{$this->auction->id}");
            assertJsonApiResponse($response);
            
            $auction = $response->json('data');
            
            // All required fields for auction detail
            expect($auction)->toHaveKey('id');
            expect($auction)->toHaveKey('title');
            expect($auction)->toHaveKey('description');
            expect($auction)->toHaveKey('condition');
            expect($auction)->toHaveKey('location');
            expect($auction)->toHaveKey('shipping_cost');
            expect($auction)->toHaveKey('starting_bid');
            expect($auction)->toHaveKey('current_bid');
            expect($auction)->toHaveKey('reserve_price');
            expect($auction)->toHaveKey('buy_now_price');
            expect($auction)->toHaveKey('start_time');
            expect($auction)->toHaveKey('end_time');
            expect($auction)->toHaveKey('status');
            expect($auction)->toHaveKey('is_featured');
            expect($auction)->toHaveKey('view_count');
            expect($auction)->toHaveKey('images');
            expect($auction)->toHaveKey('category');
            expect($auction)->toHaveKey('user');
            expect($auction)->toHaveKey('bids_count');
            expect($auction)->toHaveKey('watchers_count');
            expect($auction)->toHaveKey('time_remaining');
            expect($auction)->toHaveKey('created_at');
            expect($auction)->toHaveKey('updated_at');
        });

        test('category list API returns complete schema', function () {
            $response = $this->getJson('/api/categories');
            assertJsonApiResponse($response);
            
            $categories = $response->json('data');
            foreach ($categories as $category) {
                expect($category)->toHaveKey('id');
                expect($category)->toHaveKey('name');
                expect($category)->toHaveKey('slug');
                expect($category)->toHaveKey('description');
                expect($category)->toHaveKey('image');
                expect($category)->toHaveKey('auctions_count');
                expect($category)->toHaveKey('is_active');
            }
        });

        test('bid list API returns complete schema', function () {
            $bid = createTestBid([
                'auction_id' => $this->auction->id,
                'user_id' => $this->user->id,
            ]);

            $response = $this->getJson("/api/auctions/{$this->auction->id}/bids");
            assertJsonApiResponse($response);
            
            $bids = $response->json('data');
            foreach ($bids as $bid) {
                expect($bid)->toHaveKey('id');
                expect($bid)->toHaveKey('amount');
                expect($bid)->toHaveKey('created_at');
                expect($bid)->toHaveKey('user');
                
                // User data in bids should be limited for privacy
                expect($bid['user'])->toHaveKey('id');
                expect($bid['user'])->toHaveKey('name');
                expect($bid['user'])->toHaveKey('avatar');
                expect($bid['user'])->not->toHaveKey('email');
            }
        });
    });

    describe('Authenticated API Schema Validation', function () {
        test('user profile API returns complete schema', function () {
            $response = $this->actingAs($this->user)->getJson('/api/v1/user');
            
            if ($response->status() === 200) {
                $user = $response->json('data');
                
                expect($user)->toHaveKey('id');
                expect($user)->toHaveKey('name');
                expect($user)->toHaveKey('email');
                expect($user)->toHaveKey('avatar');
                expect($user)->toHaveKey('phone');
                expect($user)->toHaveKey('address');
                expect($user)->toHaveKey('city');
                expect($user)->toHaveKey('state');
                expect($user)->toHaveKey('zip_code');
                expect($user)->toHaveKey('country');
                expect($user)->toHaveKey('is_active');
                expect($user)->toHaveKey('email_verified_at');
                expect($user)->toHaveKey('created_at');
                expect($user)->toHaveKey('updated_at');
            }
        });

        test('user auctions API returns complete schema', function () {
            $response = $this->actingAs($this->user)->getJson('/api/v1/user/auctions');
            
            if ($response->status() === 200) {
                $auctions = $response->json('data');
                foreach ($auctions as $auction) {
                    expect($auction)->toHaveKey('id');
                    expect($auction)->toHaveKey('title');
                    expect($auction)->toHaveKey('status');
                    expect($auction)->toHaveKey('starting_bid');
                    expect($auction)->toHaveKey('current_bid');
                    expect($auction)->toHaveKey('bids_count');
                    expect($auction)->toHaveKey('watchers_count');
                    expect($auction)->toHaveKey('end_time');
                    expect($auction)->toHaveKey('images');
                }
            }
        });

        test('user bids API returns complete schema', function () {
            $bid = createTestBid([
                'auction_id' => $this->auction->id,
                'user_id' => $this->user->id,
            ]);

            $response = $this->actingAs($this->user)->getJson('/api/v1/user/bids');
            
            if ($response->status() === 200) {
                $bids = $response->json('data');
                foreach ($bids as $bid) {
                    expect($bid)->toHaveKey('id');
                    expect($bid)->toHaveKey('amount');
                    expect($bid)->toHaveKey('is_winning');
                    expect($bid)->toHaveKey('created_at');
                    expect($bid)->toHaveKey('auction');
                    
                    // Auction data in user bids
                    expect($bid['auction'])->toHaveKey('id');
                    expect($bid['auction'])->toHaveKey('title');
                    expect($bid['auction'])->toHaveKey('status');
                    expect($bid['auction'])->toHaveKey('end_time');
                    expect($bid['auction'])->toHaveKey('images');
                }
            }
        });
    });

    describe('Admin API Schema Validation', function () {
        test('admin users API returns complete schema', function () {
            $response = $this->actingAs($this->admin)->getJson('/api/v1/admin/users');
            
            if ($response->status() === 200) {
                $users = $response->json('data');
                foreach ($users as $user) {
                    expect($user)->toHaveKey('id');
                    expect($user)->toHaveKey('name');
                    expect($user)->toHaveKey('email');
                    expect($user)->toHaveKey('avatar');
                    expect($user)->toHaveKey('is_active');
                    expect($user)->toHaveKey('is_admin');
                    expect($user)->toHaveKey('email_verified_at');
                    expect($user)->toHaveKey('created_at');
                    expect($user)->toHaveKey('last_login_at');
                    expect($user)->toHaveKey('auctions_count');
                    expect($user)->toHaveKey('bids_count');
                }
            }
        });

        test('admin auctions API returns complete schema', function () {
            $response = $this->actingAs($this->admin)->getJson('/api/v1/admin/auctions');
            
            if ($response->status() === 200) {
                $auctions = $response->json('data');
                foreach ($auctions as $auction) {
                    expect($auction)->toHaveKey('id');
                    expect($auction)->toHaveKey('title');
                    expect($auction)->toHaveKey('status');
                    expect($auction)->toHaveKey('starting_bid');
                    expect($auction)->toHaveKey('current_bid');
                    expect($auction)->toHaveKey('is_featured');
                    expect($auction)->toHaveKey('user');
                    expect($auction)->toHaveKey('category');
                    expect($auction)->toHaveKey('bids_count');
                    expect($auction)->toHaveKey('created_at');
                    expect($auction)->toHaveKey('updated_at');
                }
            }
        });
    });

    describe('Real-time API Schema Validation', function () {
        test('highest bid API returns complete schema', function () {
            $bid = createTestBid([
                'auction_id' => $this->auction->id,
                'user_id' => $this->user->id,
                'amount' => 150,
            ]);

            $response = $this->getJson("/auctions/{$this->auction->id}/highest-bid");
            assertJsonApiResponse($response);
            
            $data = $response->json('data');
            expect($data)->toHaveKey('amount');
            expect($data)->toHaveKey('user');
            expect($data)->toHaveKey('created_at');
            
            expect($data['user'])->toHaveKey('id');
            expect($data['user'])->toHaveKey('name');
            expect($data['user'])->toHaveKey('avatar');
        });

        test('recent bids API returns complete schema', function () {
            createTestBid([
                'auction_id' => $this->auction->id,
                'user_id' => $this->user->id,
                'amount' => 150,
            ]);

            $response = $this->getJson("/auctions/{$this->auction->id}/recent-bids");
            assertJsonApiResponse($response);
            
            $bids = $response->json('data');
            foreach ($bids as $bid) {
                expect($bid)->toHaveKey('id');
                expect($bid)->toHaveKey('amount');
                expect($bid)->toHaveKey('created_at');
                expect($bid)->toHaveKey('user');
                
                expect($bid['user'])->toHaveKey('id');
                expect($bid['user'])->toHaveKey('name');
                expect($bid['user'])->toHaveKey('avatar');
            }
        });

        test('auction statistics API returns complete schema', function () {
            $response = $this->getJson("/api/auctions/{$this->auction->id}/statistics");
            
            if ($response->status() === 200) {
                $stats = $response->json('data');
                expect($stats)->toHaveKey('total_bids');
                expect($stats)->toHaveKey('unique_bidders');
                expect($stats)->toHaveKey('average_bid');
                expect($stats)->toHaveKey('highest_bid');
                expect($stats)->toHaveKey('time_remaining');
                expect($stats)->toHaveKey('view_count');
                expect($stats)->toHaveKey('watchers_count');
            }
        });
    });

    describe('Error Response Schema Validation', function () {
        test('validation errors return proper schema', function () {
            $response = $this->actingAs($this->user)->postJson('/api/v1/auctions', [
                'title' => '', // Invalid
            ]);

            expect($response->status())->toBe(422);
            
            $error = $response->json();
            expect($error)->toHaveKey('message');
            expect($error)->toHaveKey('errors');
            
            foreach ($error['errors'] as $field => $messages) {
                expect($messages)->toBeArray();
                foreach ($messages as $message) {
                    expect($message)->toBeString();
                }
            }
        });

        test('not found errors return proper schema', function () {
            $response = $this->getJson('/api/auctions/999999');
            expect($response->status())->toBe(404);
            
            $error = $response->json();
            expect($error)->toHaveKey('message');
            expect($error['message'])->toBeString();
        });

        test('unauthorized errors return proper schema', function () {
            $response = $this->getJson('/api/v1/user');
            expect($response->status())->toBe(401);
            
            $error = $response->json();
            expect($error)->toHaveKey('message');
            expect($error['message'])->toBeString();
        });

        test('forbidden errors return proper schema', function () {
            $response = $this->actingAs($this->user)->getJson('/api/v1/admin/users');
            expect($response->status())->toBe(403);
            
            $error = $response->json();
            expect($error)->toHaveKey('message');
            expect($error['message'])->toBeString();
        });
    });

    describe('Null Value Handling in APIs', function () {
        test('null avatar values are handled properly', function () {
            $userWithoutAvatar = createVerifiedUser(['avatar' => null]);
            $auction = createActiveAuction([
                'user_id' => $userWithoutAvatar->id,
                'category_id' => $this->category->id,
            ]);

            $response = $this->getJson("/api/auctions/{$auction->id}");
            $auctionData = $response->json('data');
            
            expect($auctionData['user'])->toHaveKey('avatar');
            expect($auctionData['user']['avatar'])->toBeNull();
        });

        test('empty collections return empty arrays not null', function () {
            $response = $this->getJson("/api/auctions/{$this->auction->id}");
            $auction = $response->json('data');
            
            expect($auction['images'])->toBeArray();
            // Should be empty array, not null
        });

        test('optional numeric fields have proper defaults', function () {
            $response = $this->getJson("/api/auctions/{$this->auction->id}");
            $auction = $response->json('data');
            
            expect($auction['current_bid'])->toBeNumeric();
            expect($auction['view_count'])->toBeNumeric();
            expect($auction['bids_count'])->toBeNumeric();
            expect($auction['watchers_count'])->toBeNumeric();
        });

        test('optional string fields handle null gracefully', function () {
            $response = $this->getJson("/api/auctions/{$this->auction->id}");
            $auction = $response->json('data');
            
            // These can be null but should have the key
            expect($auction)->toHaveKey('reserve_price');
            expect($auction)->toHaveKey('buy_now_price');
            expect($auction)->toHaveKey('shipping_cost');
        });
    });

    describe('API Response Performance', function () {
        test('API responses are optimized for frontend consumption', function () {
            testDatabaseQueryPerformance(function() {
                $this->getJson('/api/auctions');
            }, 10); // Should not require excessive queries
        });

        test('nested data is efficiently loaded', function () {
            testDatabaseQueryPerformance(function() {
                $this->getJson("/api/auctions/{$this->auction->id}");
            }, 8); // Should eager load relationships
        });
    });
});
