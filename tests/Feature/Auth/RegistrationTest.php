<?php

declare(strict_types=1);

use App\Models\User;

describe('User Registration Tests', function () {
    describe('Registration Screen Rendering', function () {
        test('registration screen can be rendered', function () {
            $response = $this->get('/register');
            assertInertiaResponse($response, 'Auth/Register');
        });

        test('registration screen redirects authenticated users', function () {
            $user = createVerifiedUser();
            $response = $this->actingAs($user)->get('/register');
            expect($response->status())->toBe(302);
        });
    });

    describe('Valid Registration Scenarios', function () {
        test('new users can register with valid data', function () {
            $userData = generateTestUserData([
                'email' => '<EMAIL>',
            ]);

            $response = $this->post('/register', $userData);

            $this->assertAuthenticated();
            assertDatabaseHasRecord('users', [
                'email' => '<EMAIL>',
                'name' => $userData['name'],
            ]);
        });

        test('registration creates user with correct default values', function () {
            $userData = generateTestUserData();

            $this->post('/register', $userData);

            $user = User::where('email', $userData['email'])->first();
            expect($user->is_active)->toBe(true);
            expect($user->is_admin)->toBe(false);
            expect($user->email_verified_at)->toBeNull();
        });

        test('registration sends email verification', function () {
            \Mail::fake();

            $userData = generateTestUserData();
            $this->post('/register', $userData);

            \Mail::assertSent(\Illuminate\Auth\Notifications\VerifyEmail::class);
        });
    });

    describe('Registration Validation', function () {
        test('registration requires name', function () {
            $userData = generateTestUserData(['name' => '']);

            $response = $this->post('/register', $userData);

            assertValidationErrors($response, ['name']);
            $this->assertGuest();
        });

        test('registration requires valid email', function () {
            $invalidEmails = ['', 'invalid', 'test@', '@example.com', '<EMAIL>'];

            foreach ($invalidEmails as $email) {
                $userData = generateTestUserData(['email' => $email]);
                $response = $this->post('/register', $userData);
                assertValidationErrors($response, ['email']);
            }
        });

        test('registration requires unique email', function () {
            $existingUser = createTestUser(['email' => '<EMAIL>']);

            $userData = generateTestUserData(['email' => '<EMAIL>']);
            $response = $this->post('/register', $userData);

            assertValidationErrors($response, ['email']);
        });

        test('registration requires password', function () {
            $userData = generateTestUserData(['password' => '']);

            $response = $this->post('/register', $userData);

            assertValidationErrors($response, ['password']);
        });

        test('registration requires password confirmation', function () {
            $userData = generateTestUserData([
                'password' => 'password123',
                'password_confirmation' => 'different',
            ]);

            $response = $this->post('/register', $userData);

            assertValidationErrors($response, ['password']);
        });

        test('registration requires minimum password length', function () {
            $userData = generateTestUserData([
                'password' => '123',
                'password_confirmation' => '123',
            ]);

            $response = $this->post('/register', $userData);

            assertValidationErrors($response, ['password']);
        });
    });

    describe('Registration Security', function () {
        test('registration is protected by CSRF', function () {
            $userData = generateTestUserData();
            testCsrfProtection('/register', $userData);
        });

        test('registration sanitizes input data', function () {
            $userData = generateTestUserData([
                'name' => '<script>alert("xss")</script>John Doe',
                'email' => '<EMAIL>',
            ]);

            $response = $this->post('/register', $userData);

            $user = User::where('email', '<EMAIL>')->first();
            expect($user->name)->not->toContain('<script>');
        });

        test('registration rate limiting works', function () {
            // Attempt multiple registrations
            for ($i = 0; $i < 6; $i++) {
                $userData = generateTestUserData([
                    'email' => "test{$i}@example.com",
                ]);
                $this->post('/register', $userData);
            }

            // Next attempt should be rate limited
            $userData = generateTestUserData(['email' => '<EMAIL>']);
            $response = $this->post('/register', $userData);

            expect($response->status())->toBe(429);
        });
    });
});
