<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'avatar',
        'phone',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'two_factor_confirmed_at',
        'is_active',
        'role',
        'last_login_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_secret',
        'two_factor_recovery_codes',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'two_factor_confirmed_at' => 'datetime',
            'is_active' => 'boolean',
            'last_login_at' => 'datetime',
        ];
    }

    /**
     * Get the user's profile.
     */
    public function profile(): HasOne
    {
        return $this->hasOne(UserProfile::class);
    }

    /**
     * Get the user's auctions.
     */
    public function auctions(): HasMany
    {
        return $this->hasMany(Auction::class, 'user_id');
    }

    /**
     * Get the user's bids.
     */
    public function bids(): HasMany
    {
        return $this->hasMany(Bid::class, 'user_id');
    }

    /**
     * Get the user's payments.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'user_id');
    }

    /**
     * Get the user's payment methods.
     */
    public function paymentMethods(): HasMany
    {
        return $this->hasMany(PaymentMethod::class, 'user_id');
    }

    /**
     * Get the user's watchlist.
     */
    public function watchlist(): HasMany
    {
        return $this->hasMany(Watchlist::class, 'user_id');
    }

    /**
     * Get the user's notifications.
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class, 'user_id');
    }

    /**
     * Get the user's won auctions.
     */
    public function wonAuctions(): HasMany
    {
        return $this->hasMany(Auction::class, 'winner_id');
    }

    /**
     * Check if user can create auctions
     */
    public function canCreateAuctions(): bool
    {
        // Admin can always create auctions
        if ($this->isAdmin()) {
            return $this->is_active && $this->hasVerifiedEmail();
        }

        // Check if user auction creation is enabled globally
        $allowUserCreation = \App\Models\Setting::get('allow_user_auction_creation', true);

        return $this->is_active &&
               $this->hasVerifiedEmail() &&
               $allowUserCreation &&
               !in_array($this->role ?? 'user', ['banned', 'suspended']);
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user is moderator
     */
    public function isModerator(): bool
    {
        return in_array($this->role ?? 'user', ['admin', 'moderator']);
    }

    /**
     * Check if user is active
     */
    public function isActive(): bool
    {
        return $this->is_active ?? true;
    }

    /**
     * Check if user's email is verified
     */
    public function isEmailVerified(): bool
    {
        return $this->hasVerifiedEmail();
    }

    /**
     * Get the is_admin attribute (accessor for frontend compatibility)
     */
    public function getIsAdminAttribute(): bool
    {
        return $this->isAdmin();
    }
}
