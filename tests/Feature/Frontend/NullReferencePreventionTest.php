<?php

declare(strict_types=1);

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;

describe('Null Reference Prevention Tests', function () {
    beforeEach(function () {
        $this->user = createVerifiedUser();
        $this->admin = createAdminUser();
        $this->category = createTestCategory();
    });

    describe('Avatar Null Reference Prevention', function () {
        test('auction listings never have null user objects', function () {
            // Create auction with user that might have null avatar
            $userWithNullAvatar = createVerifiedUser(['avatar' => null]);
            $auction = createActiveAuction([
                'user_id' => $userWithNullAvatar->id,
                'category_id' => $this->category->id,
            ]);

            $response = $this->get('/auctions');
            assertInertiaResponse($response, 'Auctions/Index');
            
            $auctions = $response->json('props.auctions.data');
            foreach ($auctions as $auctionData) {
                // User object must never be null
                expect($auctionData['user'])->not->toBeNull();
                expect($auctionData['user'])->toBeArray();
                
                // Avatar key must always exist (can be null value)
                expect($auctionData['user'])->toHaveKey('avatar');
                
                // If avatar is null, it should be explicitly null, not missing
                if ($auctionData['user']['id'] === $userWithNullAvatar->id) {
                    expect($auctionData['user']['avatar'])->toBeNull();
                }
            }
        });

        test('auction detail page never has null user object', function () {
            $userWithNullAvatar = createVerifiedUser(['avatar' => null]);
            $auction = createActiveAuction([
                'user_id' => $userWithNullAvatar->id,
                'category_id' => $this->category->id,
            ]);

            $response = $this->get("/auctions/{$auction->id}");
            assertInertiaResponse($response, 'Auctions/Show');
            
            $auctionData = $response->json('props.auction');
            
            // User object must never be null
            expect($auctionData['user'])->not->toBeNull();
            expect($auctionData['user'])->toBeArray();
            
            // Avatar key must always exist
            expect($auctionData['user'])->toHaveKey('avatar');
            expect($auctionData['user']['avatar'])->toBeNull();
        });

        test('bid listings never have null user objects', function () {
            $userWithNullAvatar = createVerifiedUser(['avatar' => null]);
            $auction = createActiveAuction(['category_id' => $this->category->id]);
            
            createTestBid([
                'auction_id' => $auction->id,
                'user_id' => $userWithNullAvatar->id,
            ]);

            $response = $this->get("/auctions/{$auction->id}/bids");
            
            if ($response->status() === 200) {
                $bids = $response->json('data') ?? $response->json();
                foreach ($bids as $bid) {
                    // User object must never be null
                    expect($bid['user'])->not->toBeNull();
                    expect($bid['user'])->toBeArray();
                    
                    // Avatar key must always exist
                    expect($bid['user'])->toHaveKey('avatar');
                }
            }
        });

        test('API responses never have null user objects', function () {
            $userWithNullAvatar = createVerifiedUser(['avatar' => null]);
            $auction = createActiveAuction([
                'user_id' => $userWithNullAvatar->id,
                'category_id' => $this->category->id,
            ]);

            $response = $this->getJson('/api/auctions');
            $auctions = $response->json('data');
            
            foreach ($auctions as $auctionData) {
                expect($auctionData['user'])->not->toBeNull();
                expect($auctionData['user'])->toBeArray();
                expect($auctionData['user'])->toHaveKey('avatar');
            }

            $detailResponse = $this->getJson("/api/auctions/{$auction->id}");
            $auctionDetail = $detailResponse->json('data');
            
            expect($auctionDetail['user'])->not->toBeNull();
            expect($auctionDetail['user'])->toBeArray();
            expect($auctionDetail['user'])->toHaveKey('avatar');
        });

        test('dashboard never has null user references', function () {
            $response = $this->actingAs($this->user)->get('/dashboard');
            assertInertiaResponse($response, 'Dashboard');
            
            $props = $response->json('props');
            
            // Auth user must never be null when authenticated
            expect($props['auth']['user'])->not->toBeNull();
            expect($props['auth']['user'])->toBeArray();
            expect($props['auth']['user'])->toHaveKey('avatar');
            
            // User auctions should have proper user data
            if (isset($props['user_auctions'])) {
                foreach ($props['user_auctions'] as $auction) {
                    if (isset($auction['user'])) {
                        expect($auction['user'])->not->toBeNull();
                        expect($auction['user'])->toHaveKey('avatar');
                    }
                }
            }
        });
    });

    describe('Category Null Reference Prevention', function () {
        test('auction listings never have null category objects', function () {
            $response = $this->get('/auctions');
            $auctions = $response->json('props.auctions.data');
            
            foreach ($auctions as $auction) {
                expect($auction['category'])->not->toBeNull();
                expect($auction['category'])->toBeArray();
                expect($auction['category'])->toHaveKey('id');
                expect($auction['category'])->toHaveKey('name');
                expect($auction['category'])->toHaveKey('slug');
            }
        });

        test('auction detail never has null category object', function () {
            $response = $this->get("/auctions/{$this->auction->id}");
            $auction = $response->json('props.auction');
            
            expect($auction['category'])->not->toBeNull();
            expect($auction['category'])->toBeArray();
            expect($auction['category'])->toHaveKey('id');
            expect($auction['category'])->toHaveKey('name');
            expect($auction['category'])->toHaveKey('slug');
        });
    });

    describe('Images Array Null Reference Prevention', function () {
        test('auction listings always have images array', function () {
            $response = $this->get('/auctions');
            $auctions = $response->json('props.auctions.data');
            
            foreach ($auctions as $auction) {
                expect($auction['images'])->not->toBeNull();
                expect($auction['images'])->toBeArray();
                // Can be empty array but must be array
            }
        });

        test('auction detail always has images array', function () {
            $response = $this->get("/auctions/{$this->auction->id}");
            $auction = $response->json('props.auction');
            
            expect($auction['images'])->not->toBeNull();
            expect($auction['images'])->toBeArray();
        });

        test('API responses always have images array', function () {
            $response = $this->getJson('/api/auctions');
            $auctions = $response->json('data');
            
            foreach ($auctions as $auction) {
                expect($auction['images'])->not->toBeNull();
                expect($auction['images'])->toBeArray();
            }
        });
    });

    describe('Numeric Field Null Reference Prevention', function () {
        test('auction numeric fields are never null', function () {
            $response = $this->get("/auctions/{$this->auction->id}");
            $auction = $response->json('props.auction');
            
            expect($auction['starting_bid'])->not->toBeNull();
            expect($auction['current_bid'])->not->toBeNull();
            expect($auction['view_count'])->not->toBeNull();
            expect($auction['bids_count'])->not->toBeNull();
            expect($auction['watchers_count'])->not->toBeNull();
            
            // Should be numeric
            expect($auction['starting_bid'])->toBeNumeric();
            expect($auction['current_bid'])->toBeNumeric();
            expect($auction['view_count'])->toBeNumeric();
            expect($auction['bids_count'])->toBeNumeric();
            expect($auction['watchers_count'])->toBeNumeric();
        });

        test('optional numeric fields handle null gracefully', function () {
            $response = $this->get("/auctions/{$this->auction->id}");
            $auction = $response->json('props.auction');
            
            // These can be null but should have keys
            expect($auction)->toHaveKey('reserve_price');
            expect($auction)->toHaveKey('buy_now_price');
            expect($auction)->toHaveKey('shipping_cost');
            
            // If not null, should be numeric
            if ($auction['reserve_price'] !== null) {
                expect($auction['reserve_price'])->toBeNumeric();
            }
            if ($auction['buy_now_price'] !== null) {
                expect($auction['buy_now_price'])->toBeNumeric();
            }
            if ($auction['shipping_cost'] !== null) {
                expect($auction['shipping_cost'])->toBeNumeric();
            }
        });
    });

    describe('Collection Null Reference Prevention', function () {
        test('paginated data never has null collections', function () {
            $response = $this->get('/auctions');
            $props = $response->json('props');
            
            expect($props['auctions'])->not->toBeNull();
            expect($props['auctions'])->toBeArray();
            expect($props['auctions']['data'])->not->toBeNull();
            expect($props['auctions']['data'])->toBeArray();
            expect($props['auctions']['links'])->not->toBeNull();
            expect($props['auctions']['links'])->toBeArray();
        });

        test('empty search results have proper structure', function () {
            $response = $this->get('/auctions?search=nonexistentitem12345');
            $props = $response->json('props');
            
            expect($props['auctions'])->not->toBeNull();
            expect($props['auctions']['data'])->not->toBeNull();
            expect($props['auctions']['data'])->toBeArray();
            expect($props['auctions']['data'])->toBeEmpty();
            expect($props['auctions']['total'])->toBe(0);
        });

        test('category listings never have null arrays', function () {
            $response = $this->get('/categories');
            $props = $response->json('props');
            
            expect($props['categories'])->not->toBeNull();
            expect($props['categories'])->toBeArray();
        });
    });

    describe('Authentication State Null Reference Prevention', function () {
        test('guest auth state is properly structured', function () {
            $response = $this->get('/');
            $props = $response->json('props');
            
            expect($props['auth'])->not->toBeNull();
            expect($props['auth'])->toBeArray();
            expect($props['auth'])->toHaveKey('user');
            expect($props['auth']['user'])->toBeNull(); // Should be explicitly null for guests
        });

        test('authenticated auth state is properly structured', function () {
            $response = $this->actingAs($this->user)->get('/');
            $props = $response->json('props');
            
            expect($props['auth'])->not->toBeNull();
            expect($props['auth'])->toBeArray();
            expect($props['auth'])->toHaveKey('user');
            expect($props['auth']['user'])->not->toBeNull();
            expect($props['auth']['user'])->toBeArray();
            expect($props['auth']['user'])->toHaveKey('avatar');
        });
    });

    describe('Error State Null Reference Prevention', function () {
        test('validation errors have proper structure', function () {
            $response = $this->actingAs($this->user)->post('/auctions', [
                'title' => '', // Invalid
            ]);

            if ($response->status() === 422) {
                $props = $response->json('props');
                expect($props['errors'])->not->toBeNull();
                expect($props['errors'])->toBeArray();
            }
        });

        test('flash messages have proper structure', function () {
            $response = $this->get('/');
            $props = $response->json('props');
            
            expect($props['flash'])->not->toBeNull();
            expect($props['flash'])->toBeArray();
            expect($props['flash'])->toHaveKey('success');
            expect($props['flash'])->toHaveKey('error');
            expect($props['flash'])->toHaveKey('warning');
            expect($props['flash'])->toHaveKey('info');
        });
    });

    describe('Relationship Loading Prevention', function () {
        test('eager loading prevents N+1 and null references', function () {
            // Create multiple auctions to test N+1 prevention
            for ($i = 0; $i < 5; $i++) {
                createActiveAuction(['category_id' => $this->category->id]);
            }

            testDatabaseQueryPerformance(function() {
                $response = $this->get('/auctions');
                $auctions = $response->json('props.auctions.data');
                
                // Verify all relationships are loaded
                foreach ($auctions as $auction) {
                    expect($auction['user'])->not->toBeNull();
                    expect($auction['category'])->not->toBeNull();
                    expect($auction['images'])->not->toBeNull();
                }
            }, 10); // Should not require excessive queries
        });

        test('API eager loading prevents null references', function () {
            testDatabaseQueryPerformance(function() {
                $response = $this->getJson('/api/auctions');
                $auctions = $response->json('data');
                
                foreach ($auctions as $auction) {
                    expect($auction['user'])->not->toBeNull();
                    expect($auction['category'])->not->toBeNull();
                    expect($auction['images'])->not->toBeNull();
                }
            }, 8);
        });
    });

    describe('Data Transformation Consistency', function () {
        test('all data transformations maintain consistent structure', function () {
            $pages = [
                '/auctions',
                "/auctions/{$this->auction->id}",
                '/categories',
                "/categories/{$this->category->slug}",
            ];

            foreach ($pages as $page) {
                $response = $this->get($page);
                
                if ($response->status() === 200) {
                    $props = $response->json('props');
                    
                    // Global props should always be consistent
                    expect($props)->toHaveKey('auth');
                    expect($props)->toHaveKey('csrf_token');
                    expect($props)->toHaveKey('flash');
                    expect($props)->toHaveKey('errors');
                    
                    expect($props['auth'])->not->toBeNull();
                    expect($props['flash'])->not->toBeNull();
                    expect($props['errors'])->not->toBeNull();
                }
            }
        });
    });
});
