<?php

declare(strict_types=1);

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;
use App\Models\Bid;

describe('Data Serialization Tests', function () {
    beforeEach(function () {
        $this->user = createVerifiedUser();
        $this->admin = createAdminUser();
        $this->category = createTestCategory();
        $this->auction = createActiveAuction([
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
        ]);
    });

    describe('User Model Serialization', function () {
        test('user model serializes with required fields', function () {
            $userArray = $this->user->toArray();
            
            expect($userArray)->toHaveKey('id');
            expect($userArray)->toHaveKey('name');
            expect($userArray)->toHaveKey('email');
            expect($userArray)->toHaveKey('avatar');
            expect($userArray)->toHaveKey('phone');
            expect($userArray)->toHaveKey('address');
            expect($userArray)->toHaveKey('city');
            expect($userArray)->toHaveKey('state');
            expect($userArray)->toHaveKey('zip_code');
            expect($userArray)->toHaveKey('country');
            expect($userArray)->toHaveKey('is_active');
            expect($userArray)->toHaveKey('is_admin');
            expect($userArray)->toHaveKey('email_verified_at');
            expect($userArray)->toHaveKey('created_at');
            expect($userArray)->toHaveKey('updated_at');
        });

        test('user model hides sensitive fields', function () {
            $userArray = $this->user->toArray();
            
            expect($userArray)->not->toHaveKey('password');
            expect($userArray)->not->toHaveKey('remember_token');
            expect($userArray)->not->toHaveKey('email_verification_token');
        });

        test('user model handles null avatar gracefully', function () {
            $userWithoutAvatar = createVerifiedUser(['avatar' => null]);
            $userArray = $userWithoutAvatar->toArray();
            
            expect($userArray)->toHaveKey('avatar');
            expect($userArray['avatar'])->toBeNull();
        });

        test('user model provides avatar URL when present', function () {
            $userWithAvatar = createVerifiedUser(['avatar' => 'avatars/test.jpg']);
            $userArray = $userWithAvatar->toArray();
            
            expect($userArray)->toHaveKey('avatar');
            expect($userArray['avatar'])->toBeString();
        });

        test('user model includes computed attributes', function () {
            $userArray = $this->user->toArray();
            
            // Should include computed fields like full_name, display_name, etc.
            if (array_key_exists('full_name', $userArray)) {
                expect($userArray['full_name'])->toBeString();
            }
            
            if (array_key_exists('initials', $userArray)) {
                expect($userArray['initials'])->toBeString();
            }
        });
    });

    describe('Auction Model Serialization', function () {
        test('auction model serializes with required fields', function () {
            $auctionArray = $this->auction->toArray();
            
            expect($auctionArray)->toHaveKey('id');
            expect($auctionArray)->toHaveKey('title');
            expect($auctionArray)->toHaveKey('description');
            expect($auctionArray)->toHaveKey('condition');
            expect($auctionArray)->toHaveKey('location');
            expect($auctionArray)->toHaveKey('starting_bid');
            expect($auctionArray)->toHaveKey('current_bid');
            expect($auctionArray)->toHaveKey('reserve_price');
            expect($auctionArray)->toHaveKey('buy_now_price');
            expect($auctionArray)->toHaveKey('shipping_cost');
            expect($auctionArray)->toHaveKey('start_time');
            expect($auctionArray)->toHaveKey('end_time');
            expect($auctionArray)->toHaveKey('status');
            expect($auctionArray)->toHaveKey('is_featured');
            expect($auctionArray)->toHaveKey('view_count');
            expect($auctionArray)->toHaveKey('user_id');
            expect($auctionArray)->toHaveKey('category_id');
            expect($auctionArray)->toHaveKey('created_at');
            expect($auctionArray)->toHaveKey('updated_at');
        });

        test('auction model includes computed attributes', function () {
            $auctionArray = $this->auction->toArray();
            
            // Should include computed fields
            if (array_key_exists('time_remaining', $auctionArray)) {
                expect($auctionArray['time_remaining'])->toBeString();
            }
            
            if (array_key_exists('is_ending_soon', $auctionArray)) {
                expect($auctionArray['is_ending_soon'])->toBeBool();
            }
            
            if (array_key_exists('has_reserve', $auctionArray)) {
                expect($auctionArray['has_reserve'])->toBeBool();
            }
        });

        test('auction model handles null values properly', function () {
            $auctionArray = $this->auction->toArray();
            
            // Optional fields should be present even if null
            expect($auctionArray)->toHaveKey('reserve_price');
            expect($auctionArray)->toHaveKey('buy_now_price');
            expect($auctionArray)->toHaveKey('shipping_cost');
        });

        test('auction model with relationships serializes correctly', function () {
            $auction = $this->auction->load(['user', 'category', 'images']);
            $auctionArray = $auction->toArray();
            
            expect($auctionArray)->toHaveKey('user');
            expect($auctionArray)->toHaveKey('category');
            expect($auctionArray)->toHaveKey('images');
            
            // User relationship should have avatar
            expect($auctionArray['user'])->toHaveKey('avatar');
            
            // Images should be array
            expect($auctionArray['images'])->toBeArray();
        });
    });

    describe('Category Model Serialization', function () {
        test('category model serializes with required fields', function () {
            $categoryArray = $this->category->toArray();
            
            expect($categoryArray)->toHaveKey('id');
            expect($categoryArray)->toHaveKey('name');
            expect($categoryArray)->toHaveKey('slug');
            expect($categoryArray)->toHaveKey('description');
            expect($categoryArray)->toHaveKey('image');
            expect($categoryArray)->toHaveKey('is_active');
            expect($categoryArray)->toHaveKey('sort_order');
            expect($categoryArray)->toHaveKey('created_at');
            expect($categoryArray)->toHaveKey('updated_at');
        });

        test('category model includes computed attributes', function () {
            $categoryArray = $this->category->toArray();
            
            if (array_key_exists('auctions_count', $categoryArray)) {
                expect($categoryArray['auctions_count'])->toBeNumeric();
            }
            
            if (array_key_exists('active_auctions_count', $categoryArray)) {
                expect($categoryArray['active_auctions_count'])->toBeNumeric();
            }
        });

        test('category model handles null image gracefully', function () {
            $categoryWithoutImage = createTestCategory(['image' => null]);
            $categoryArray = $categoryWithoutImage->toArray();
            
            expect($categoryArray)->toHaveKey('image');
            expect($categoryArray['image'])->toBeNull();
        });
    });

    describe('Bid Model Serialization', function () {
        test('bid model serializes with required fields', function () {
            $bid = createTestBid([
                'auction_id' => $this->auction->id,
                'user_id' => $this->user->id,
            ]);
            
            $bidArray = $bid->toArray();
            
            expect($bidArray)->toHaveKey('id');
            expect($bidArray)->toHaveKey('auction_id');
            expect($bidArray)->toHaveKey('user_id');
            expect($bidArray)->toHaveKey('amount');
            expect($bidArray)->toHaveKey('created_at');
            expect($bidArray)->toHaveKey('updated_at');
        });

        test('bid model with relationships serializes correctly', function () {
            $bid = createTestBid([
                'auction_id' => $this->auction->id,
                'user_id' => $this->user->id,
            ]);
            
            $bid = $bid->load(['user', 'auction']);
            $bidArray = $bid->toArray();
            
            expect($bidArray)->toHaveKey('user');
            expect($bidArray)->toHaveKey('auction');
            
            // User should have avatar
            expect($bidArray['user'])->toHaveKey('avatar');
            
            // Auction should have basic info
            expect($bidArray['auction'])->toHaveKey('title');
            expect($bidArray['auction'])->toHaveKey('status');
        });

        test('bid model includes computed attributes', function () {
            $bid = createTestBid([
                'auction_id' => $this->auction->id,
                'user_id' => $this->user->id,
            ]);
            
            $bidArray = $bid->toArray();
            
            if (array_key_exists('is_winning', $bidArray)) {
                expect($bidArray['is_winning'])->toBeBool();
            }
            
            if (array_key_exists('time_ago', $bidArray)) {
                expect($bidArray['time_ago'])->toBeString();
            }
        });
    });

    describe('API Resource Serialization', function () {
        test('user resource serializes correctly for public view', function () {
            // Test public user resource (for auction listings)
            $response = $this->get('/api/auctions');
            $auctions = $response->json('data');
            
            foreach ($auctions as $auction) {
                $user = $auction['user'];
                
                expect($user)->toHaveKey('id');
                expect($user)->toHaveKey('name');
                expect($user)->toHaveKey('avatar');
                expect($user)->toHaveKey('created_at');
                
                // Should not expose email in public view
                expect($user)->not->toHaveKey('email');
                expect($user)->not->toHaveKey('phone');
            }
        });

        test('user resource serializes correctly for private view', function () {
            $response = $this->actingAs($this->user)->getJson('/api/v1/user');
            
            if ($response->status() === 200) {
                $user = $response->json('data');
                
                expect($user)->toHaveKey('id');
                expect($user)->toHaveKey('name');
                expect($user)->toHaveKey('email');
                expect($user)->toHaveKey('avatar');
                expect($user)->toHaveKey('phone');
                expect($user)->toHaveKey('address');
                
                // Should still not expose sensitive data
                expect($user)->not->toHaveKey('password');
                expect($user)->not->toHaveKey('remember_token');
            }
        });

        test('auction resource includes all required fields', function () {
            $response = $this->getJson("/api/auctions/{$this->auction->id}");
            $auction = $response->json('data');
            
            expect($auction)->toHaveKey('id');
            expect($auction)->toHaveKey('title');
            expect($auction)->toHaveKey('description');
            expect($auction)->toHaveKey('images');
            expect($auction)->toHaveKey('user');
            expect($auction)->toHaveKey('category');
            expect($auction)->toHaveKey('time_remaining');
            expect($auction)->toHaveKey('bids_count');
            expect($auction)->toHaveKey('watchers_count');
            
            // User should have avatar
            expect($auction['user'])->toHaveKey('avatar');
            
            // Images should be array
            expect($auction['images'])->toBeArray();
        });
    });

    describe('Collection Serialization', function () {
        test('auction collection serializes consistently', function () {
            // Create multiple auctions
            for ($i = 0; $i < 3; $i++) {
                createActiveAuction(['category_id' => $this->category->id]);
            }

            $response = $this->getJson('/api/auctions');
            $auctions = $response->json('data');
            
            foreach ($auctions as $auction) {
                expect($auction)->toHaveKey('id');
                expect($auction)->toHaveKey('title');
                expect($auction)->toHaveKey('user');
                expect($auction)->toHaveKey('category');
                expect($auction)->toHaveKey('images');
                
                // Each user should have avatar field
                expect($auction['user'])->toHaveKey('avatar');
                
                // Each auction should have images array
                expect($auction['images'])->toBeArray();
            }
        });

        test('empty collections serialize as empty arrays', function () {
            // Clear all auctions
            Auction::query()->delete();

            $response = $this->getJson('/api/auctions');
            $data = $response->json('data');
            
            expect($data)->toBeArray();
            expect($data)->toBeEmpty();
        });

        test('paginated collections include metadata', function () {
            $response = $this->getJson('/api/auctions');
            $responseData = $response->json();
            
            expect($responseData)->toHaveKey('data');
            expect($responseData)->toHaveKey('current_page');
            expect($responseData)->toHaveKey('last_page');
            expect($responseData)->toHaveKey('per_page');
            expect($responseData)->toHaveKey('total');
        });
    });

    describe('Date Serialization', function () {
        test('dates are serialized in consistent format', function () {
            $auctionArray = $this->auction->toArray();
            
            expect($auctionArray['created_at'])->toBeString();
            expect($auctionArray['updated_at'])->toBeString();
            expect($auctionArray['start_time'])->toBeString();
            expect($auctionArray['end_time'])->toBeString();
            
            // Should be in ISO format
            expect($auctionArray['created_at'])->toMatch('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/');
        });

        test('null dates are handled properly', function () {
            $userArray = $this->user->toArray();
            
            if (array_key_exists('email_verified_at', $userArray)) {
                expect($userArray['email_verified_at'])->toBeOneOf([null, 'string']);
            }
        });
    });

    describe('Numeric Serialization', function () {
        test('numeric fields are properly typed', function () {
            $auctionArray = $this->auction->toArray();
            
            expect($auctionArray['starting_bid'])->toBeNumeric();
            expect($auctionArray['current_bid'])->toBeNumeric();
            expect($auctionArray['view_count'])->toBeNumeric();
            
            if ($auctionArray['reserve_price'] !== null) {
                expect($auctionArray['reserve_price'])->toBeNumeric();
            }
        });

        test('boolean fields are properly typed', function () {
            $auctionArray = $this->auction->toArray();
            
            expect($auctionArray['is_featured'])->toBeBool();
            
            $userArray = $this->user->toArray();
            expect($userArray['is_active'])->toBeBool();
            expect($userArray['is_admin'])->toBeBool();
        });
    });

    describe('Performance of Serialization', function () {
        test('large collections serialize efficiently', function () {
            // Create many auctions
            for ($i = 0; $i < 50; $i++) {
                createActiveAuction(['category_id' => $this->category->id]);
            }

            assertExecutionTimeUnder(function() {
                $this->getJson('/api/auctions');
            }, 3.0);
        });

        test('nested relationships serialize efficiently', function () {
            assertExecutionTimeUnder(function() {
                $this->getJson("/api/auctions/{$this->auction->id}");
            }, 2.0);
        });
    });
});
