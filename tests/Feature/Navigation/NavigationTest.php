<?php

declare(strict_types=1);

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;

describe('Navigation & Routing Tests', function () {
    beforeEach(function () {
        $this->user = createVerifiedUser();
        $this->admin = createAdminUser();
        $this->category = createTestCategory(['slug' => 'test-category']);
        $this->auction = createActiveAuction([
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
        ]);
    });

    describe('Public Route Navigation', function () {
        test('home page loads and renders correctly', function () {
            $response = $this->get('/');
            assertInertiaResponse($response, 'Home');
            expect($response->json('props.featured_auctions'))->toBeArray();
        });

        test('auction listing pages load correctly', function () {
            $routes = [
                '/' => 'Home',
                '/auctions' => 'Auctions/Index',
                '/auctions/featured' => 'Auctions/Featured',
                '/auctions/ending-soon' => 'Auctions/EndingSoon',
            ];

            foreach ($routes as $route => $component) {
                $response = $this->get($route);
                assertInertiaResponse($response, $component);
            }
        });

        test('category pages load correctly', function () {
            $routes = [
                '/categories' => 'Categories/Index',
                "/categories/{$this->category->slug}" => 'Categories/Show',
                "/categories/{$this->category->slug}/auctions" => 'Categories/Auctions',
            ];

            foreach ($routes as $route => $component) {
                $response = $this->get($route);
                assertInertiaResponse($response, $component);
            }
        });

        test('auction detail page loads correctly', function () {
            $response = $this->get("/auctions/{$this->auction->id}");
            assertInertiaResponse($response, 'Auctions/Show');
            expect($response->json('props.auction.id'))->toBe($this->auction->id);
        });
    });

    describe('Protected Route Navigation', function () {
        test('authenticated user can access dashboard', function () {
            $response = $this->actingAs($this->user)->get('/dashboard');
            assertInertiaResponse($response, 'Dashboard');
        });

        test('authenticated user can access auction creation', function () {
            $response = $this->actingAs($this->user)->get('/auctions/create');
            assertInertiaResponse($response, 'Auctions/Create');
        });

        test('authenticated user can access settings pages', function () {
            $routes = [
                '/settings/profile' => 'Settings/Profile',
                '/settings/password' => 'Settings/Password',
                '/settings/appearance' => 'Settings/Appearance',
            ];

            foreach ($routes as $route => $component) {
                $response = $this->actingAs($this->user)->get($route);
                assertInertiaResponse($response, $component);
            }
        });

        test('guests are redirected to login for protected routes', function () {
            $protectedRoutes = [
                '/dashboard',
                '/auctions/create',
                '/settings/profile',
                '/settings/password',
            ];

            foreach ($protectedRoutes as $route) {
                $response = $this->get($route);
                assertPageRequiresAuth($response, $route);
            }
        });
    });

    describe('Admin Route Navigation', function () {
        test('admin can access admin dashboard', function () {
            $response = $this->actingAs($this->admin)->get('/admin');
            assertInertiaResponse($response, 'Admin/Dashboard');
        });

        test('admin can access admin management pages', function () {
            $routes = [
                '/admin/users' => 'Admin/Users/<USER>',
                '/admin/auctions' => 'Admin/Auctions/Index',
                '/admin/settings' => 'Admin/Settings/Index',
            ];

            foreach ($routes as $route => $component) {
                $response = $this->actingAs($this->admin)->get($route);
                assertInertiaResponse($response, $component);
            }
        });

        test('regular users cannot access admin routes', function () {
            $adminRoutes = [
                '/admin',
                '/admin/users',
                '/admin/auctions',
                '/admin/settings',
            ];

            foreach ($adminRoutes as $route) {
                $response = $this->actingAs($this->user)->get($route);
                assertPageRequiresAdmin($response, $route);
            }
        });
    });

    describe('Dynamic Route Parameters', function () {
        test('auction routes accept valid numeric IDs', function () {
            $validIds = ['1', '123', '999999'];

            foreach ($validIds as $id) {
                $response = $this->get("/auctions/{$id}");
                // Should pass middleware validation (404 if resource doesn't exist)
                expect($response->status())->toBeIn([200, 404]);
            }
        });

        test('auction routes reject invalid IDs', function () {
            $invalidIds = ['abc', 'null', '-1', '12.5', ''];

            foreach ($invalidIds as $id) {
                $response = $this->get("/auctions/{$id}");
                expect($response->status())->toBe(404);
            }
        });

        test('category routes work with valid slugs', function () {
            $response = $this->get("/categories/{$this->category->slug}");
            assertInertiaResponse($response, 'Categories/Show');
        });

        test('category routes handle invalid slugs', function () {
            $response = $this->get('/categories/non-existent-category');
            expect($response->status())->toBe(404);
        });

        test('nested routes work correctly', function () {
            $response = $this->get("/categories/{$this->category->slug}/auctions");
            assertInertiaResponse($response, 'Categories/Auctions');
            expect($response->json('props.category.slug'))->toBe($this->category->slug);
        });
    });

    describe('Route Redirects', function () {
        test('settings root redirects to profile', function () {
            $response = $this->actingAs($this->user)->get('/settings');
            expect($response->status())->toBe(302);
            expect($response->headers->get('Location'))->toContain('/settings/profile');
        });

        test('admin root redirects to dashboard', function () {
            $response = $this->actingAs($this->admin)->get('/admin');
            expect($response->status())->toBeIn([200, 302]);
        });

        test('authenticated users redirected from auth pages', function () {
            $authRoutes = ['/login', '/register'];

            foreach ($authRoutes as $route) {
                $response = $this->actingAs($this->user)->get($route);
                expect($response->status())->toBe(302);
                expect($response->headers->get('Location'))->toContain('/dashboard');
            }
        });
    });

    describe('Route Performance', function () {
        test('public routes load within acceptable time', function () {
            $routes = ['/', '/auctions', '/categories'];

            foreach ($routes as $route) {
                testPageLoadPerformance($route, 2.0);
            }
        });

        test('protected routes load within acceptable time', function () {
            $routes = ['/dashboard', '/settings/profile'];

            foreach ($routes as $route) {
                assertExecutionTimeUnder(function() use ($route) {
                    $this->actingAs($this->user)->get($route);
                }, 2.0);
            }
        });

        test('database queries are optimized for route loading', function () {
            testDatabaseQueryPerformance(function() {
                $this->get('/auctions');
            }, 15); // Should not exceed 15 queries for auction listing
        });
    });

    describe('Route Security', function () {
        test('routes are protected against path traversal', function () {
            $maliciousPaths = [
                '/auctions/../../../etc/passwd',
                '/categories/..%2F..%2F..%2Fetc%2Fpasswd',
                '/admin/..%252f..%252f..%252fetc%252fpasswd',
            ];

            foreach ($maliciousPaths as $path) {
                $response = $this->get($path);
                expect($response->status())->toBeIn([404, 400]);
            }
        });

        test('routes handle malformed URLs gracefully', function () {
            $malformedUrls = [
                '/auctions/%00',
                '/categories/%0A%0D',
                '/admin/%3Cscript%3E',
            ];

            foreach ($malformedUrls as $url) {
                $response = $this->get($url);
                expect($response->status())->toBeIn([404, 400]);
            }
        });
    });
});
