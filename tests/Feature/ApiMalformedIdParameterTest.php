<?php

declare(strict_types=1);

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;
use App\Models\Bid;
use App\Models\Payment;

describe('API Malformed ID Parameter Tests', function () {
    beforeEach(function () {
        $this->user = User::factory()->create([
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
    });

    describe('API Auction Routes - Malformed ID Tests', function () {
        test('API auction show with malformed ID returns 404 JSON', function () {
            $malformedIds = ['abc', 'null', 'undefined', '12.5', '-1', '0', '../../etc/passwd'];
            
            foreach ($malformedIds as $id) {
                $response = $this->getJson("/api/auctions/{$id}");
                expect($response->status())->toBe(404, "Failed for ID: {$id}");
                expect($response->json())->toHaveKey('message');
            }
        });

        test('API auction update with malformed ID returns 404 JSON', function () {
            $this->actingAs($this->user);
            
            $malformedIds = ['abc', 'null', 'undefined', '12.5', '-1', '0'];
            
            foreach ($malformedIds as $id) {
                $response = $this->putJson("/api/auctions/{$id}", [
                    'title' => 'Test Title',
                    'description' => 'Test Description',
                ]);
                expect($response->status())->toBe(404, "Failed for ID: {$id}");
                expect($response->json())->toHaveKey('message');
            }
        });

        test('API auction delete with malformed ID returns 404 JSON', function () {
            $this->actingAs($this->user);
            
            $malformedIds = ['abc', 'null', 'undefined', '12.5', '-1', '0'];
            
            foreach ($malformedIds as $id) {
                $response = $this->deleteJson("/api/auctions/{$id}");
                expect($response->status())->toBe(404, "Failed for ID: {$id}");
                expect($response->json())->toHaveKey('message');
            }
        });

        test('API auction activate with malformed ID returns 404 JSON', function () {
            $this->actingAs($this->user);
            
            $malformedIds = ['abc', 'null', 'undefined', '12.5', '-1', '0'];
            
            foreach ($malformedIds as $id) {
                $response = $this->postJson("/api/auctions/{$id}/activate");
                expect($response->status())->toBe(404, "Failed for ID: {$id}");
                expect($response->json())->toHaveKey('message');
            }
        });

        test('API auction end with malformed ID returns 404 JSON', function () {
            $this->actingAs($this->user);
            
            $malformedIds = ['abc', 'null', 'undefined', '12.5', '-1', '0'];
            
            foreach ($malformedIds as $id) {
                $response = $this->postJson("/api/auctions/{$id}/end");
                expect($response->status())->toBe(404, "Failed for ID: {$id}");
                expect($response->json())->toHaveKey('message');
            }
        });

        test('API auction bids with malformed ID returns 404 JSON', function () {
            $malformedIds = ['abc', 'null', 'undefined', '12.5', '-1', '0'];
            
            foreach ($malformedIds as $id) {
                $response = $this->getJson("/api/auctions/{$id}/bids");
                expect($response->status())->toBe(404, "Failed for ID: {$id}");
                expect($response->json())->toHaveKey('message');
            }
        });

        test('API auction statistics with malformed ID returns 404 JSON', function () {
            $malformedIds = ['abc', 'null', 'undefined', '12.5', '-1', '0'];
            
            foreach ($malformedIds as $id) {
                $response = $this->getJson("/api/auctions/{$id}/statistics");
                expect($response->status())->toBe(404, "Failed for ID: {$id}");
                expect($response->json())->toHaveKey('message');
            }
        });

        test('API auction highest bid with malformed ID returns 404 JSON', function () {
            $malformedIds = ['abc', 'null', 'undefined', '12.5', '-1', '0'];
            
            foreach ($malformedIds as $id) {
                $response = $this->getJson("/api/auctions/{$id}/highest-bid");
                expect($response->status())->toBe(404, "Failed for ID: {$id}");
                expect($response->json())->toHaveKey('message');
            }
        });

        test('API auction winning bid with malformed ID returns 404 JSON', function () {
            $malformedIds = ['abc', 'null', 'undefined', '12.5', '-1', '0'];
            
            foreach ($malformedIds as $id) {
                $response = $this->getJson("/api/auctions/{$id}/winning-bid");
                expect($response->status())->toBe(404, "Failed for ID: {$id}");
                expect($response->json())->toHaveKey('message');
            }
        });

        test('API auction recent bids with malformed ID returns 404 JSON', function () {
            $malformedIds = ['abc', 'null', 'undefined', '12.5', '-1', '0'];
            
            foreach ($malformedIds as $id) {
                $response = $this->getJson("/api/auctions/{$id}/recent-bids");
                expect($response->status())->toBe(404, "Failed for ID: {$id}");
                expect($response->json())->toHaveKey('message');
            }
        });
    });

    describe('API Bid Routes - Malformed ID Tests', function () {
        test('API bid show with malformed ID returns 404 JSON', function () {
            $this->actingAs($this->user);
            
            $malformedIds = ['abc', 'null', 'undefined', '12.5', '-1', '0'];
            
            foreach ($malformedIds as $id) {
                $response = $this->getJson("/api/bids/{$id}");
                expect($response->status())->toBe(404, "Failed for ID: {$id}");
                expect($response->json())->toHaveKey('message');
            }
        });

        test('API bid delete with malformed ID returns 404 JSON', function () {
            $this->actingAs($this->user);
            
            $malformedIds = ['abc', 'null', 'undefined', '12.5', '-1', '0'];
            
            foreach ($malformedIds as $id) {
                $response = $this->deleteJson("/api/bids/{$id}");
                expect($response->status())->toBe(404, "Failed for ID: {$id}");
                expect($response->json())->toHaveKey('message');
            }
        });
    });

    describe('API Category Routes - Malformed ID Tests', function () {
        test('API category show with malformed ID returns 404 JSON', function () {
            $malformedIds = ['abc', 'null', 'undefined', '12.5', '-1', '0'];
            
            foreach ($malformedIds as $id) {
                $response = $this->getJson("/api/categories/{$id}");
                expect($response->status())->toBe(404, "Failed for ID: {$id}");
                expect($response->json())->toHaveKey('message');
            }
        });

        test('API category auctions with malformed ID returns 404 JSON', function () {
            $malformedIds = ['abc', 'null', 'undefined', '12.5', '-1', '0'];
            
            foreach ($malformedIds as $id) {
                $response = $this->getJson("/api/categories/{$id}/auctions");
                expect($response->status())->toBe(404, "Failed for ID: {$id}");
                expect($response->json())->toHaveKey('message');
            }
        });
    });

    describe('API Payment Routes - Malformed ID Tests', function () {
        test('API payment show with malformed ID returns 404 JSON', function () {
            $this->actingAs($this->user);
            
            $malformedIds = ['abc', 'null', 'undefined', '12.5', '-1', '0'];
            
            foreach ($malformedIds as $id) {
                $response = $this->getJson("/api/payments/{$id}");
                expect($response->status())->toBe(404, "Failed for ID: {$id}");
                expect($response->json())->toHaveKey('message');
            }
        });

        test('API payment method delete with malformed ID returns 404 JSON', function () {
            $this->actingAs($this->user);
            
            $malformedIds = ['abc', 'null', 'undefined', '12.5', '-1', '0'];
            
            foreach ($malformedIds as $id) {
                $response = $this->deleteJson("/api/payments/methods/{$id}");
                expect($response->status())->toBe(404, "Failed for ID: {$id}");
                expect($response->json())->toHaveKey('message');
            }
        });
    });

    describe('API Watchlist Routes - Malformed ID Tests', function () {
        test('API watchlist delete with malformed auction ID returns 404 JSON', function () {
            $this->actingAs($this->user);
            
            $malformedIds = ['abc', 'null', 'undefined', '12.5', '-1', '0'];
            
            foreach ($malformedIds as $id) {
                $response = $this->deleteJson("/api/watchlist/{$id}");
                expect($response->status())->toBe(404, "Failed for ID: {$id}");
                expect($response->json())->toHaveKey('message');
            }
        });
    });

    describe('Edge Cases - Special Characters and Injection Attempts', function () {
        test('SQL injection attempts in ID parameters return 404', function () {
            $injectionAttempts = [
                "1' OR '1'='1",
                "1; DROP TABLE auctions;--",
                "1 UNION SELECT * FROM users",
                "'; DELETE FROM auctions; --",
            ];
            
            foreach ($injectionAttempts as $id) {
                $response = $this->getJson("/api/auctions/" . urlencode($id));
                expect($response->status())->toBe(404, "Failed for injection: {$id}");
            }
        });

        test('XSS attempts in ID parameters return 404', function () {
            $xssAttempts = [
                "<script>alert('xss')</script>",
                "javascript:alert(1)",
                "<img src=x onerror=alert(1)>",
                "';alert(String.fromCharCode(88,83,83))//",
            ];
            
            foreach ($xssAttempts as $id) {
                $response = $this->getJson("/api/auctions/" . urlencode($id));
                expect($response->status())->toBe(404, "Failed for XSS: {$id}");
            }
        });

        test('Path traversal attempts in ID parameters return 404', function () {
            $pathTraversalAttempts = [
                "../../etc/passwd",
                "../../../windows/system32/drivers/etc/hosts",
                "....//....//....//etc/passwd",
                "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            ];
            
            foreach ($pathTraversalAttempts as $id) {
                $response = $this->getJson("/api/auctions/" . urlencode($id));
                expect($response->status())->toBe(404, "Failed for path traversal: {$id}");
            }
        });
    });
});
