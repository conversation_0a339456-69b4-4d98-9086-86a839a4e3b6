<?php

declare(strict_types=1);

use App\Models\User;
use App\Models\Auction;
use App\Models\Bid;

describe('Bid CRUD Operations Tests', function () {
    beforeEach(function () {
        $this->user = createVerifiedUser();
        $this->auctionOwner = createVerifiedUser();
        $this->admin = createAdminUser();
        $this->category = createTestCategory();
        $this->auction = createActiveAuction([
            'user_id' => $this->auctionOwner->id,
            'category_id' => $this->category->id,
            'starting_bid' => 100,
        ]);
    });

    describe('Bid Creation (Create)', function () {
        test('authenticated user can place bid on auction', function () {
            $bidData = generateTestBidData($this->auction->id, [
                'amount' => 150,
            ]);

            $response = $this->actingAs($this->user)->post('/bids', $bidData);

            expect($response->status())->toBeIn([201, 302]);
            assertDatabaseHasRecord('bids', [
                'auction_id' => $this->auction->id,
                'user_id' => $this->user->id,
                'amount' => 150,
            ]);
        });

        test('guest cannot place bid', function () {
            $bidData = generateTestBidData($this->auction->id);

            $response = $this->post('/bids', $bidData);

            assertPageRequiresAuth($response);
        });

        test('auction owner cannot bid on own auction', function () {
            $bidData = generateTestBidData($this->auction->id);

            $response = $this->actingAs($this->auctionOwner)->post('/bids', $bidData);

            expect($response->status())->toBeIn([422, 403]);
            assertDatabaseMissingRecord('bids', [
                'auction_id' => $this->auction->id,
                'user_id' => $this->auctionOwner->id,
            ]);
        });

        test('bid amount must be higher than current highest bid', function () {
            // Place initial bid
            createTestBid([
                'auction_id' => $this->auction->id,
                'user_id' => createVerifiedUser()->id,
                'amount' => 150,
            ]);

            // Try to place lower bid
            $bidData = generateTestBidData($this->auction->id, [
                'amount' => 140,
            ]);

            $response = $this->actingAs($this->user)->post('/bids', $bidData);

            assertValidationErrors($response, ['amount']);
        });

        test('bid amount must meet minimum increment', function () {
            // Place initial bid
            createTestBid([
                'auction_id' => $this->auction->id,
                'user_id' => createVerifiedUser()->id,
                'amount' => 150,
            ]);

            // Try to place bid with insufficient increment
            $bidData = generateTestBidData($this->auction->id, [
                'amount' => 151, // Assuming minimum increment is more than $1
            ]);

            $response = $this->actingAs($this->user)->post('/bids', $bidData);

            // This might pass or fail depending on minimum increment rules
            expect($response->status())->toBeIn([201, 302, 422]);
        });

        test('cannot bid on ended auction', function () {
            $endedAuction = createEndedAuction([
                'user_id' => $this->auctionOwner->id,
                'category_id' => $this->category->id,
            ]);

            $bidData = generateTestBidData($endedAuction->id);

            $response = $this->actingAs($this->user)->post('/bids', $bidData);

            expect($response->status())->toBeIn([422, 403]);
        });

        test('bid validates required fields', function () {
            $invalidData = [
                'auction_id' => '',
                'amount' => '',
            ];

            $response = $this->actingAs($this->user)->post('/bids', $invalidData);

            assertValidationErrors($response, ['auction_id', 'amount']);
        });

        test('bid validates data types', function () {
            $invalidData = [
                'auction_id' => 'not-a-number',
                'amount' => 'not-a-number',
            ];

            $response = $this->actingAs($this->user)->post('/bids', $invalidData);

            assertValidationErrors($response, ['auction_id', 'amount']);
        });

        test('bid creation updates auction highest bid', function () {
            $bidAmount = 150;
            $bidData = generateTestBidData($this->auction->id, [
                'amount' => $bidAmount,
            ]);

            $this->actingAs($this->user)->post('/bids', $bidData);

            $this->auction->refresh();
            expect($this->auction->current_bid)->toBe($bidAmount);
        });
    });

    describe('Bid Reading (Read)', function () {
        beforeEach(function () {
            $this->bid = createTestBid([
                'auction_id' => $this->auction->id,
                'user_id' => $this->user->id,
                'amount' => 150,
            ]);
        });

        test('anyone can view auction bids', function () {
            $response = $this->get("/auctions/{$this->auction->id}/bids");
            
            expect($response->status())->toBe(200);
            $bids = $response->json('data') ?? $response->json();
            expect($bids)->toBeArray();
        });

        test('bid owner can view their bid details', function () {
            $response = $this->actingAs($this->user)->getJson("/api/v1/bids/{$this->bid->id}");
            
            if ($response->status() === 200) {
                expect($response->json('id'))->toBe($this->bid->id);
                expect($response->json('amount'))->toBe($this->bid->amount);
            }
        });

        test('non-owner cannot view private bid details', function () {
            $otherUser = createVerifiedUser();
            $response = $this->actingAs($otherUser)->getJson("/api/v1/bids/{$this->bid->id}");
            
            expect($response->status())->toBeIn([403, 404]);
        });

        test('admin can view any bid details', function () {
            $response = $this->actingAs($this->admin)->getJson("/api/v1/bids/{$this->bid->id}");
            
            if ($response->status() === 200) {
                expect($response->json('id'))->toBe($this->bid->id);
            }
        });

        test('bid listing supports pagination', function () {
            // Create multiple bids
            for ($i = 0; $i < 25; $i++) {
                createTestBid([
                    'auction_id' => $this->auction->id,
                    'user_id' => createVerifiedUser()->id,
                    'amount' => 150 + $i,
                ]);
            }

            $response = $this->get("/auctions/{$this->auction->id}/bids?page=1");
            
            if ($response->status() === 200) {
                $data = $response->json();
                expect($data)->toHaveKey('current_page');
                expect($data)->toHaveKey('last_page');
            }
        });

        test('bid listing shows highest bid first', function () {
            // Create multiple bids
            createTestBid([
                'auction_id' => $this->auction->id,
                'user_id' => createVerifiedUser()->id,
                'amount' => 200,
            ]);
            createTestBid([
                'auction_id' => $this->auction->id,
                'user_id' => createVerifiedUser()->id,
                'amount' => 175,
            ]);

            $response = $this->get("/auctions/{$this->auction->id}/bids");
            
            if ($response->status() === 200) {
                $bids = $response->json('data') ?? $response->json();
                if (!empty($bids)) {
                    expect($bids[0]['amount'])->toBeGreaterThanOrEqual($bids[1]['amount'] ?? 0);
                }
            }
        });
    });

    describe('Bid Updating (Update)', function () {
        beforeEach(function () {
            $this->bid = createTestBid([
                'auction_id' => $this->auction->id,
                'user_id' => $this->user->id,
                'amount' => 150,
            ]);
        });

        test('bids cannot be updated after placement', function () {
            $updateData = ['amount' => 200];

            $response = $this->actingAs($this->user)->put("/bids/{$this->bid->id}", $updateData);

            // Bids should be immutable once placed
            expect($response->status())->toBeIn([405, 403, 422]);
        });

        test('admin cannot update bids', function () {
            $updateData = ['amount' => 200];

            $response = $this->actingAs($this->admin)->put("/bids/{$this->bid->id}", $updateData);

            // Even admins shouldn't be able to modify bids
            expect($response->status())->toBeIn([405, 403, 422]);
        });
    });

    describe('Bid Deletion (Delete)', function () {
        beforeEach(function () {
            $this->bid = createTestBid([
                'auction_id' => $this->auction->id,
                'user_id' => $this->user->id,
                'amount' => 150,
            ]);
        });

        test('bid owner can retract bid if allowed', function () {
            $response = $this->actingAs($this->user)->delete("/bids/{$this->bid->id}");

            // Depending on business rules, this might be allowed or not
            expect($response->status())->toBeIn([200, 302, 403, 422]);
        });

        test('non-owner cannot delete bid', function () {
            $otherUser = createVerifiedUser();
            $response = $this->actingAs($otherUser)->delete("/bids/{$this->bid->id}");

            expect($response->status())->toBeIn([403, 404]);
        });

        test('admin can delete bids', function () {
            $response = $this->actingAs($this->admin)->delete("/bids/{$this->bid->id}");

            expect($response->status())->toBeIn([200, 302]);
        });

        test('cannot delete winning bid after auction ends', function () {
            // End the auction with this as the winning bid
            $this->auction->update([
                'status' => 'ended',
                'end_time' => now()->subMinute(),
            ]);

            $response = $this->actingAs($this->user)->delete("/bids/{$this->bid->id}");

            expect($response->status())->toBeIn([403, 422]);
        });

        test('deleting bid updates auction highest bid', function () {
            // Create a higher bid
            $higherBid = createTestBid([
                'auction_id' => $this->auction->id,
                'user_id' => createVerifiedUser()->id,
                'amount' => 200,
            ]);

            $this->auction->refresh();
            expect($this->auction->current_bid)->toBe(200);

            // Delete the higher bid
            $this->actingAs($this->admin)->delete("/bids/{$higherBid->id}");

            $this->auction->refresh();
            expect($this->auction->current_bid)->toBe(150); // Should revert to previous highest
        });
    });

    describe('Bid Business Logic', function () {
        test('automatic bidding works correctly', function () {
            // This would test proxy/automatic bidding if implemented
            $this->markTestSkipped('Automatic bidding testing requires specific implementation');
        });

        test('bid notifications are sent', function () {
            \Notification::fake();

            $bidData = generateTestBidData($this->auction->id, [
                'amount' => 150,
            ]);

            $this->actingAs($this->user)->post('/bids', $bidData);

            // Should notify auction owner of new bid
            \Notification::assertSentTo($this->auctionOwner, \App\Notifications\NewBidNotification::class);
        });

        test('bid history is maintained', function () {
            // Create multiple bids
            $amounts = [150, 175, 200];
            foreach ($amounts as $amount) {
                createTestBid([
                    'auction_id' => $this->auction->id,
                    'user_id' => createVerifiedUser()->id,
                    'amount' => $amount,
                ]);
            }

            $response = $this->get("/auctions/{$this->auction->id}/bids");
            
            if ($response->status() === 200) {
                $bids = $response->json('data') ?? $response->json();
                expect(count($bids))->toBe(3);
            }
        });
    });
});
