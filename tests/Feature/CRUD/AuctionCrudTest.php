<?php

declare(strict_types=1);

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;

describe('Auction CRUD Operations Tests', function () {
    beforeEach(function () {
        $this->user = createVerifiedUser();
        $this->admin = createAdminUser();
        $this->category = createTestCategory();
        $this->auction = createActiveAuction([
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
        ]);
    });

    describe('Auction Creation (Create)', function () {
        test('authenticated user can view auction creation form', function () {
            $response = $this->actingAs($this->user)->get('/auctions/create');
            assertInertiaResponse($response, 'Auctions/Create');
            
            $props = $response->json('props');
            expect($props)->toHaveKey('categories');
            expect($props['categories'])->toBeArray();
        });

        test('guest cannot access auction creation form', function () {
            $response = $this->get('/auctions/create');
            assertPageRequiresAuth($response);
        });

        test('user can create auction with valid data', function () {
            $auctionData = generateTestAuctionData([
                'category_id' => $this->category->id,
            ]);

            $response = $this->actingAs($this->user)->post('/auctions', $auctionData);

            expect($response->status())->toBeIn([201, 302]);
            assertDatabaseHasRecord('auctions', [
                'title' => $auctionData['title'],
                'user_id' => $this->user->id,
                'category_id' => $this->category->id,
            ]);
        });

        test('auction creation validates required fields', function () {
            $invalidData = [
                'title' => '',
                'description' => '',
                'starting_bid' => '',
                'category_id' => '',
            ];

            $response = $this->actingAs($this->user)->post('/auctions', $invalidData);

            assertValidationErrors($response, ['title', 'description', 'starting_bid', 'category_id']);
        });

        test('auction creation validates data types', function () {
            $invalidData = generateTestAuctionData([
                'starting_bid' => 'not-a-number',
                'reserve_price' => 'invalid',
                'category_id' => 'not-an-id',
                'end_time' => 'invalid-date',
            ]);

            $response = $this->actingAs($this->user)->post('/auctions', $invalidData);

            assertValidationErrors($response, ['starting_bid', 'reserve_price', 'category_id', 'end_time']);
        });

        test('auction creation validates business rules', function () {
            $invalidData = generateTestAuctionData([
                'starting_bid' => 100,
                'reserve_price' => 50, // Reserve should be >= starting bid
                'buy_now_price' => 25, // Buy now should be >= reserve
                'end_time' => now()->subDay()->format('Y-m-d H:i:s'), // End time in past
            ]);

            $response = $this->actingAs($this->user)->post('/auctions', $invalidData);

            assertValidationErrors($response, ['reserve_price', 'buy_now_price', 'end_time']);
        });

        test('auction creation sets correct default values', function () {
            $auctionData = generateTestAuctionData([
                'category_id' => $this->category->id,
            ]);

            $this->actingAs($this->user)->post('/auctions', $auctionData);

            $auction = Auction::where('title', $auctionData['title'])->first();
            expect($auction->user_id)->toBe($this->user->id);
            expect($auction->status)->toBe('pending');
            expect($auction->is_featured)->toBe(false);
            expect($auction->view_count)->toBe(0);
        });
    });

    describe('Auction Reading (Read)', function () {
        test('anyone can view auction listing page', function () {
            $response = $this->get('/auctions');
            assertInertiaResponse($response, 'Auctions/Index');
            
            $props = $response->json('props');
            expect($props)->toHaveKey('auctions');
            expect($props['auctions']['data'])->toBeArray();
        });

        test('anyone can view individual auction', function () {
            $response = $this->get("/auctions/{$this->auction->id}");
            assertInertiaResponse($response, 'Auctions/Show');
            
            $props = $response->json('props');
            expect($props['auction']['id'])->toBe($this->auction->id);
            expect($props['auction']['title'])->toBe($this->auction->title);
        });

        test('auction view increments view count', function () {
            $initialViewCount = $this->auction->view_count;

            $this->get("/auctions/{$this->auction->id}");

            $this->auction->refresh();
            expect($this->auction->view_count)->toBe($initialViewCount + 1);
        });

        test('auction listing supports pagination', function () {
            // Create multiple auctions
            for ($i = 0; $i < 25; $i++) {
                createActiveAuction(['category_id' => $this->category->id]);
            }

            $response = $this->get('/auctions?page=1');
            $props = $response->json('props');
            
            expect($props['auctions'])->toHaveKey('current_page');
            expect($props['auctions'])->toHaveKey('last_page');
            expect($props['auctions'])->toHaveKey('per_page');
        });

        test('auction listing supports filtering', function () {
            $response = $this->get("/auctions?category={$this->category->id}");
            assertInertiaResponse($response, 'Auctions/Index');
            
            $auctions = $response->json('props.auctions.data');
            foreach ($auctions as $auction) {
                expect($auction['category_id'])->toBe($this->category->id);
            }
        });

        test('auction listing supports sorting', function () {
            $response = $this->get('/auctions?sort=ending_soon');
            assertInertiaResponse($response, 'Auctions/Index');
            
            $auctions = $response->json('props.auctions.data');
            expect($auctions)->toBeArray();
        });

        test('non-existent auction returns 404', function () {
            $response = $this->get('/auctions/999999');
            expect($response->status())->toBe(404);
        });
    });

    describe('Auction Updating (Update)', function () {
        test('auction owner can view edit form', function () {
            $response = $this->actingAs($this->user)->get("/auctions/{$this->auction->id}/edit");
            
            if ($response->status() === 200) {
                assertInertiaResponse($response, 'Auctions/Edit');
                expect($response->json('props.auction.id'))->toBe($this->auction->id);
            }
        });

        test('non-owner cannot view edit form', function () {
            $otherUser = createVerifiedUser();
            $response = $this->actingAs($otherUser)->get("/auctions/{$this->auction->id}/edit");
            assertPageRequiresAdmin($response);
        });

        test('admin can view any auction edit form', function () {
            $response = $this->actingAs($this->admin)->get("/auctions/{$this->auction->id}/edit");
            
            if ($response->status() === 200) {
                assertInertiaResponse($response, 'Auctions/Edit');
            }
        });

        test('auction owner can update auction', function () {
            $updateData = [
                'title' => 'Updated Auction Title',
                'description' => 'Updated description',
                'reserve_price' => 250,
            ];

            $response = $this->actingAs($this->user)->put("/auctions/{$this->auction->id}", $updateData);

            expect($response->status())->toBeIn([200, 302]);
            assertDatabaseHasRecord('auctions', [
                'id' => $this->auction->id,
                'title' => 'Updated Auction Title',
            ]);
        });

        test('auction update validates data', function () {
            $invalidData = [
                'title' => '',
                'starting_bid' => -10,
                'end_time' => 'invalid-date',
            ];

            $response = $this->actingAs($this->user)->put("/auctions/{$this->auction->id}", $invalidData);

            assertValidationErrors($response, ['title', 'starting_bid', 'end_time']);
        });

        test('cannot update auction with active bids', function () {
            // Add a bid to the auction
            createTestBid([
                'auction_id' => $this->auction->id,
                'user_id' => createVerifiedUser()->id,
                'amount' => $this->auction->starting_bid + 10,
            ]);

            $updateData = ['starting_bid' => 200];
            $response = $this->actingAs($this->user)->put("/auctions/{$this->auction->id}", $updateData);

            // Should prevent updating critical fields when bids exist
            expect($response->status())->toBeIn([422, 403]);
        });
    });

    describe('Auction Deletion (Delete)', function () {
        test('auction owner can delete auction without bids', function () {
            $response = $this->actingAs($this->user)->delete("/auctions/{$this->auction->id}");

            expect($response->status())->toBeIn([200, 302]);
            assertDatabaseMissingRecord('auctions', ['id' => $this->auction->id]);
        });

        test('non-owner cannot delete auction', function () {
            $otherUser = createVerifiedUser();
            $response = $this->actingAs($otherUser)->delete("/auctions/{$this->auction->id}");
            assertPageRequiresAdmin($response);
        });

        test('admin can delete any auction', function () {
            $response = $this->actingAs($this->admin)->delete("/auctions/{$this->auction->id}");

            expect($response->status())->toBeIn([200, 302]);
        });

        test('cannot delete auction with active bids', function () {
            // Add a bid to the auction
            createTestBid([
                'auction_id' => $this->auction->id,
                'user_id' => createVerifiedUser()->id,
                'amount' => $this->auction->starting_bid + 10,
            ]);

            $response = $this->actingAs($this->user)->delete("/auctions/{$this->auction->id}");

            expect($response->status())->toBeIn([422, 403]);
            assertDatabaseHasRecord('auctions', ['id' => $this->auction->id]);
        });

        test('deleting auction removes associated data', function () {
            // This would test cascade deletes or cleanup
            $this->markTestSkipped('Cascade delete testing requires specific implementation');
        });
    });
});
