<?php

/*
|--------------------------------------------------------------------------
| Test Case
|--------------------------------------------------------------------------
|
| The closure you provide to your test functions is always bound to a specific PHPUnit test
| case class. By default, that class is "PHPUnit\Framework\TestCase". Of course, you may
| need to change it using the "pest()" function to bind a different classes or traits.
|
*/

pest()->extend(Tests\TestCase::class)
    ->use(Illuminate\Foundation\Testing\RefreshDatabase::class)
    ->in('Feature');

/*
|--------------------------------------------------------------------------
| Expectations
|--------------------------------------------------------------------------
|
| When you're writing tests, you often need to check that values meet certain conditions. The
| "expect()" function gives you access to a set of "expectations" methods that you can use
| to assert different things. Of course, you may extend the Expectation API at any time.
|
*/

expect()->extend('toBeOne', function () {
    return $this->toBe(1);
});

expect()->extend('toBeNotFound', function () {
    return $this->toBe(404);
});

expect()->extend('toBeServerError', function () {
    return $this->toBe(500);
});

/*
|--------------------------------------------------------------------------
| Functions
|--------------------------------------------------------------------------
|
| While Pest is very powerful out-of-the-box, you may have some testing code specific to your
| project that you don't want to repeat in every file. Here you can also expose helpers as
| global functions to help you to reduce the number of lines of code in your test files.
|
*/

// =============================================================================
// BASIC MODEL FACTORIES
// =============================================================================

function createTestUser(array $attributes = []): \App\Models\User
{
    return \App\Models\User::factory()->create($attributes);
}

function createTestCategory(array $attributes = []): \App\Models\Category
{
    return \App\Models\Category::factory()->create($attributes);
}

function createTestAuction(array $attributes = []): \App\Models\Auction
{
    return \App\Models\Auction::factory()->create($attributes);
}

function createTestBid(array $attributes = []): \App\Models\Bid
{
    return \App\Models\Bid::factory()->create($attributes);
}

// =============================================================================
// SPECIALIZED USER FACTORIES
// =============================================================================

function createAdminUser(array $attributes = []): \App\Models\User
{
    return \App\Models\User::factory()->create(array_merge([
        'is_admin' => true,
        'is_active' => true,
        'email_verified_at' => now(),
    ], $attributes));
}

function createVerifiedUser(array $attributes = []): \App\Models\User
{
    return \App\Models\User::factory()->create(array_merge([
        'is_active' => true,
        'email_verified_at' => now(),
    ], $attributes));
}

function createUnverifiedUser(array $attributes = []): \App\Models\User
{
    return \App\Models\User::factory()->create(array_merge([
        'is_active' => true,
        'email_verified_at' => null,
    ], $attributes));
}

function createInactiveUser(array $attributes = []): \App\Models\User
{
    return \App\Models\User::factory()->create(array_merge([
        'is_active' => false,
        'email_verified_at' => now(),
    ], $attributes));
}

// =============================================================================
// SPECIALIZED AUCTION FACTORIES
// =============================================================================

function createActiveAuction(array $attributes = []): \App\Models\Auction
{
    $user = createVerifiedUser();
    $category = createTestCategory();

    return \App\Models\Auction::factory()->create(array_merge([
        'user_id' => $user->id,
        'category_id' => $category->id,
        'status' => 'active',
        'start_time' => now()->subHour(),
        'end_time' => now()->addDays(7),
        'is_featured' => false,
    ], $attributes));
}

function createEndedAuction(array $attributes = []): \App\Models\Auction
{
    $user = createVerifiedUser();
    $category = createTestCategory();

    return \App\Models\Auction::factory()->create(array_merge([
        'user_id' => $user->id,
        'category_id' => $category->id,
        'status' => 'ended',
        'start_time' => now()->subDays(8),
        'end_time' => now()->subDay(),
    ], $attributes));
}

function createFeaturedAuction(array $attributes = []): \App\Models\Auction
{
    $user = createVerifiedUser();
    $category = createTestCategory();

    return \App\Models\Auction::factory()->create(array_merge([
        'user_id' => $user->id,
        'category_id' => $category->id,
        'status' => 'active',
        'start_time' => now()->subHour(),
        'end_time' => now()->addDays(7),
        'is_featured' => true,
    ], $attributes));
}

function createEndingSoonAuction(array $attributes = []): \App\Models\Auction
{
    $user = createVerifiedUser();
    $category = createTestCategory();

    return \App\Models\Auction::factory()->create(array_merge([
        'user_id' => $user->id,
        'category_id' => $category->id,
        'status' => 'active',
        'start_time' => now()->subDays(6),
        'end_time' => now()->addHours(2), // Ending in 2 hours
    ], $attributes));
}

// =============================================================================
// COMPLEX SCENARIO BUILDERS
// =============================================================================

function createAuctionWithBids(int $bidCount = 3, array $auctionAttributes = []): array
{
    $auction = createActiveAuction($auctionAttributes);
    $bids = [];

    for ($i = 0; $i < $bidCount; $i++) {
        $bidder = createVerifiedUser();
        $bids[] = createTestBid([
            'auction_id' => $auction->id,
            'user_id' => $bidder->id,
            'amount' => $auction->starting_bid + (($i + 1) * 10),
            'created_at' => now()->subMinutes($bidCount - $i),
        ]);
    }

    return ['auction' => $auction, 'bids' => $bids];
}

function createCategoryWithAuctions(int $auctionCount = 5, array $categoryAttributes = []): array
{
    $category = createTestCategory($categoryAttributes);
    $auctions = [];

    for ($i = 0; $i < $auctionCount; $i++) {
        $auctions[] = createActiveAuction(['category_id' => $category->id]);
    }

    return ['category' => $category, 'auctions' => $auctions];
}

function createUserWithAuctions(int $auctionCount = 3, array $userAttributes = []): array
{
    $user = createVerifiedUser($userAttributes);
    $auctions = [];

    for ($i = 0; $i < $auctionCount; $i++) {
        $category = createTestCategory();
        $auctions[] = createTestAuction([
            'user_id' => $user->id,
            'category_id' => $category->id,
        ]);
    }

    return ['user' => $user, 'auctions' => $auctions];
}

// =============================================================================
// TEST DATA GENERATORS
// =============================================================================

function generateTestAuctionData(array $overrides = []): array
{
    return array_merge([
        'title' => 'Test Auction ' . uniqid(),
        'description' => 'This is a test auction description with sufficient detail.',
        'starting_bid' => 100,
        'reserve_price' => 200,
        'buy_now_price' => 500,
        'start_time' => now()->addHour()->format('Y-m-d H:i:s'),
        'end_time' => now()->addDays(7)->format('Y-m-d H:i:s'),
        'condition' => 'new',
        'shipping_cost' => 10,
        'location' => 'Test City, Test State',
    ], $overrides);
}

function generateTestUserData(array $overrides = []): array
{
    $faker = \Faker\Factory::create();

    return array_merge([
        'name' => $faker->name,
        'email' => $faker->unique()->safeEmail,
        'password' => 'password',
        'password_confirmation' => 'password',
        'phone' => $faker->phoneNumber,
        'address' => $faker->address,
        'city' => $faker->city,
        'state' => $faker->state,
        'zip_code' => $faker->postcode,
        'country' => 'United States',
    ], $overrides);
}

function generateTestBidData(int $auctionId, array $overrides = []): array
{
    return array_merge([
        'auction_id' => $auctionId,
        'amount' => 150,
    ], $overrides);
}

// =============================================================================
// TEST ASSERTION HELPERS
// =============================================================================

function assertPageLoadsSuccessfully(\Illuminate\Testing\TestResponse $response, string $routeName = ''): void
{
    $message = $routeName ? "Route '{$routeName}' should load successfully" : 'Page should load successfully';
    expect($response->status())->toBeIn([200, 302], $message);
}

function assertPageRequiresAuth(\Illuminate\Testing\TestResponse $response, string $routeName = ''): void
{
    $message = $routeName ? "Route '{$routeName}' should require authentication" : 'Page should require authentication';
    expect($response->status())->toBe(302, $message);
    expect($response->headers->get('Location'))->toContain('/login');
}

function assertPageRequiresAdmin(\Illuminate\Testing\TestResponse $response, string $routeName = ''): void
{
    $message = $routeName ? "Route '{$routeName}' should require admin access" : 'Page should require admin access';
    expect($response->status())->toBeIn([403, 302], $message);
}

function assertValidationErrors(\Illuminate\Testing\TestResponse $response, array $expectedFields): void
{
    expect($response->status())->toBe(422, 'Should return validation error status');

    $errors = $response->json('errors') ?? [];
    foreach ($expectedFields as $field) {
        expect($errors)->toHaveKey($field, "Should have validation error for field: {$field}");
    }
}

function assertJsonApiResponse(\Illuminate\Testing\TestResponse $response, int $expectedStatus = 200): void
{
    expect($response->status())->toBe($expectedStatus);
    expect($response->headers->get('Content-Type'))->toContain('application/json');
}

function assertInertiaResponse(\Illuminate\Testing\TestResponse $response, string $component = null): void
{
    expect($response->status())->toBe(200);
    expect($response->headers->get('X-Inertia'))->toBe('true');

    if ($component) {
        expect($response->json('component'))->toBe($component);
    }
}

// =============================================================================
// ROUTE TESTING UTILITIES
// =============================================================================

function testPublicRoutes(array $routes): void
{
    foreach ($routes as $route) {
        $response = test()->get($route);
        assertPageLoadsSuccessfully($response, $route);
    }
}

function testProtectedRoutes(array $routes): void
{
    foreach ($routes as $route) {
        $response = test()->get($route);
        assertPageRequiresAuth($response, $route);
    }
}

function testAdminRoutes(array $routes): void
{
    $user = createVerifiedUser();

    foreach ($routes as $route) {
        $response = test()->actingAs($user)->get($route);
        assertPageRequiresAdmin($response, $route);
    }
}

function testApiRoutes(array $routes): void
{
    foreach ($routes as $route) {
        $response = test()->getJson($route);
        assertJsonApiResponse($response);
    }
}

// =============================================================================
// FORM TESTING UTILITIES
// =============================================================================

function testFormValidation(string $route, array $validData, array $invalidFields, string $method = 'POST'): void
{
    // Test with valid data
    $response = test()->call($method, $route, $validData);
    expect($response->status())->not->toBe(422, 'Valid data should not return validation errors');

    // Test each invalid field
    foreach ($invalidFields as $field => $invalidValue) {
        $invalidData = array_merge($validData, [$field => $invalidValue]);
        $response = test()->call($method, $route, $invalidData);
        assertValidationErrors($response, [$field]);
    }
}

function testCsrfProtection(string $route, array $data = [], string $method = 'POST'): void
{
    $response = test()->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
        ->call($method, $route, $data);

    // Should fail without CSRF token
    expect($response->status())->toBe(419, 'Should require CSRF token');
}

// =============================================================================
// DATABASE TESTING UTILITIES
// =============================================================================

function assertDatabaseHasRecord(string $table, array $data): void
{
    test()->assertDatabaseHas($table, $data);
}

function assertDatabaseMissingRecord(string $table, array $data): void
{
    test()->assertDatabaseMissing($table, $data);
}

function assertDatabaseCount(string $table, int $count): void
{
    test()->assertDatabaseCount($table, $count);
}

function seedTestData(): array
{
    $admin = createAdminUser(['email' => '<EMAIL>']);
    $users = [];
    $categories = [];
    $auctions = [];

    // Create test users
    for ($i = 0; $i < 5; $i++) {
        $users[] = createVerifiedUser();
    }

    // Create test categories
    $categoryNames = ['Electronics', 'Collectibles', 'Art', 'Books', 'Jewelry'];
    foreach ($categoryNames as $name) {
        $categories[] = createTestCategory(['name' => $name]);
    }

    // Create test auctions
    foreach ($categories as $category) {
        for ($i = 0; $i < 3; $i++) {
            $user = $users[array_rand($users)];
            $auctions[] = createActiveAuction([
                'user_id' => $user->id,
                'category_id' => $category->id,
            ]);
        }
    }

    return [
        'admin' => $admin,
        'users' => $users,
        'categories' => $categories,
        'auctions' => $auctions,
    ];
}

// =============================================================================
// PERFORMANCE TESTING UTILITIES
// =============================================================================

function measureExecutionTime(callable $callback): float
{
    $startTime = microtime(true);
    $callback();
    $endTime = microtime(true);

    return $endTime - $startTime;
}

function assertExecutionTimeUnder(callable $callback, float $maxSeconds): void
{
    $executionTime = measureExecutionTime($callback);
    expect($executionTime)->toBeLessThan($maxSeconds,
        "Execution should complete in under {$maxSeconds} seconds, took {$executionTime} seconds");
}

function testPageLoadPerformance(string $route, float $maxSeconds = 2.0): void
{
    assertExecutionTimeUnder(function() use ($route) {
        test()->get($route);
    }, $maxSeconds);
}

function testDatabaseQueryPerformance(callable $callback, int $maxQueries = 10): void
{
    \DB::enableQueryLog();
    $callback();
    $queries = \DB::getQueryLog();
    \DB::disableQueryLog();

    expect(count($queries))->toBeLessThanOrEqual($maxQueries,
        "Should execute no more than {$maxQueries} queries, executed " . count($queries));
}

// =============================================================================
// ERROR TESTING UTILITIES
// =============================================================================

function testErrorPage(int $statusCode, string $route = null): void
{
    if ($route) {
        $response = test()->get($route);
        expect($response->status())->toBe($statusCode);
    }

    // Test that error page renders properly
    $response = test()->get("/test-error/{$statusCode}");
    expect($response->status())->toBe($statusCode);
}

function testMaliciousInput(string $route, array $maliciousInputs, string $method = 'POST'): void
{
    foreach ($maliciousInputs as $input) {
        $response = test()->call($method, $route, $input);
        expect($response->status())->toBeIn([400, 404, 422],
            'Malicious input should be rejected: ' . json_encode($input));
    }
}
