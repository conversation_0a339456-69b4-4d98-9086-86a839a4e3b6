<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Actions\Auction\ActivateAuctionAction;
use App\Actions\Auction\CreateAuctionAction;
use App\Actions\Auction\EndAuctionAction;
use App\Actions\Auction\UpdateAuctionAction;
use App\Actions\Auction\UploadAuctionImagesAction;
use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\User\ValueObjects\UserId;
use App\Http\Controllers\Controller;
use App\Http\Requests\CreateAuctionRequest;
use App\Http\Requests\UpdateAuctionRequest;
use App\Http\Requests\UploadAuctionImagesRequest;
use App\Http\Resources\AuctionResource;
use App\Http\Resources\AuctionCollection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class AuctionController extends Controller
{
    public function __construct(
        private AuctionRepositoryInterface $auctionRepository
    ) {}

    /**
     * Display a listing of auctions
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = min($request->get('per_page', 15), 50);
        $status = $request->get('status');
        $category = $request->get('category');
        $search = $request->get('search');

        if ($search) {
            $auctions = $this->auctionRepository->search($search, [
                'status' => $status,
                'category_id' => $category,
            ], $perPage);
        } elseif ($status) {
            $auctions = $this->auctionRepository->findByStatus(
                new \App\Domain\Auction\ValueObjects\AuctionStatus($status),
                $perPage
            );
        } elseif ($category) {
            $auctions = $this->auctionRepository->findByCategory(
                Id::fromString($category),
                $perPage
            );
        } else {
            $auctions = $this->auctionRepository->findActive($perPage);
        }

        return response()->json(new AuctionCollection($auctions));
    }

    /**
     * Store a newly created auction
     */
    public function store(CreateAuctionRequest $request, CreateAuctionAction $action): JsonResponse
    {
        try {
            $auction = $action->execute($request->validated());

            return response()->json([
                'message' => 'Auction created successfully',
                'data' => new AuctionResource($auction),
            ], Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create auction',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Display the specified auction
     */
    public function show(int $id): JsonResponse
    {
        try {
            $auction = $this->auctionRepository->findByIdOrFail(Id::fromString((string) $id));

            // Increment view count
            $this->auctionRepository->incrementViewCount(Id::fromString((string) $id));

            return response()->json([
                'data' => new AuctionResource($auction),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Auction not found',
            ], Response::HTTP_NOT_FOUND);
        }
    }

    /**
     * Update the specified auction
     */
    public function update(
        int $id,
        UpdateAuctionRequest $request,
        UpdateAuctionAction $action
    ): JsonResponse {
        try {
            $userId = UserId::fromString((string) auth()->id());
            $auction = $action->execute($id, $request->validated(), $userId);

            return response()->json([
                'message' => 'Auction updated successfully',
                'data' => new AuctionResource($auction),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update auction',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Remove the specified auction
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $auction = $this->auctionRepository->findByIdOrFail(Id::fromString((string) $id));
            $userId = UserId::fromString((string) auth()->id());

            // Check permissions
            if (!$auction->sellerId()->equals($userId)) {
                return response()->json([
                    'message' => 'Unauthorized',
                ], Response::HTTP_FORBIDDEN);
            }

            // Check if auction can be deleted
            if (!$auction->status()->isDraft()) {
                return response()->json([
                    'message' => 'Only draft auctions can be deleted',
                ], Response::HTTP_BAD_REQUEST);
            }

            $this->auctionRepository->delete($auction);

            return response()->json([
                'message' => 'Auction deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete auction',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Activate an auction
     */
    public function activate(int $id, ActivateAuctionAction $action): JsonResponse
    {
        try {
            $userId = UserId::fromString((string) auth()->id());
            $auction = $action->execute($id, $userId);

            return response()->json([
                'message' => 'Auction activated successfully',
                'data' => new AuctionResource($auction),
            ]);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'message' => 'Auction not found',
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to activate auction',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * End an auction
     */
    public function end(int $id, EndAuctionAction $action): JsonResponse
    {
        try {
            $userId = UserId::fromString((string) auth()->id());
            $auction = $action->execute($id, $userId);

            return response()->json([
                'message' => 'Auction ended successfully',
                'data' => new AuctionResource($auction),
            ]);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'message' => 'Auction not found',
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to end auction',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Upload images for an auction
     */
    public function uploadImages(
        int $id,
        UploadAuctionImagesRequest $request,
        UploadAuctionImagesAction $action
    ): JsonResponse {
        try {
            $userId = UserId::fromString((string) auth()->id());
            $images = $action->execute($id, $request->file('images'), $userId);

            return response()->json([
                'message' => 'Images uploaded successfully',
                'data' => $images,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to upload images',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Get featured auctions
     */
    public function featured(Request $request): JsonResponse
    {
        $perPage = min($request->get('per_page', 15), 50);
        $auctions = $this->auctionRepository->findFeatured($perPage);

        return response()->json(new AuctionCollection($auctions));
    }

    /**
     * Get ending soon auctions
     */
    public function endingSoon(Request $request): JsonResponse
    {
        $perPage = min($request->get('per_page', 15), 50);
        $hours = $request->get('hours', 24);
        $auctions = $this->auctionRepository->findEndingSoon($hours, $perPage);

        return response()->json(new AuctionCollection($auctions));
    }

    /**
     * Get auction statistics
     */
    public function statistics(): JsonResponse
    {
        $stats = $this->auctionRepository->getStatistics();

        return response()->json([
            'data' => $stats,
        ]);
    }

    /**
     * Get user's auctions
     */
    public function userAuctions(Request $request): JsonResponse
    {
        $userId = UserId::fromString((string) auth()->id());
        $perPage = min($request->get('per_page', 15), 50);
        $auctions = $this->auctionRepository->findBySeller($userId, $perPage);

        return response()->json(new AuctionCollection($auctions));
    }

    /**
     * Get user's watched auctions
     */
    public function watchedAuctions(Request $request): JsonResponse
    {
        $userId = UserId::fromString((string) auth()->id());
        $perPage = min($request->get('per_page', 15), 50);
        $auctions = $this->auctionRepository->findWatchedByUser($userId, $perPage);

        return response()->json(new AuctionCollection($auctions));
    }

    /**
     * Get user's won auctions
     */
    public function wonAuctions(Request $request): JsonResponse
    {
        $userId = UserId::fromString((string) auth()->id());
        $perPage = min($request->get('per_page', 15), 50);
        $auctions = $this->auctionRepository->findWonByUser($userId, $perPage);

        return response()->json(new AuctionCollection($auctions));
    }
}
