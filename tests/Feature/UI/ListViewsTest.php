<?php

declare(strict_types=1);

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;

describe('List Views and Pagination Tests', function () {
    beforeEach(function () {
        $this->user = createVerifiedUser();
        $this->admin = createAdminUser();
        $this->category = createTestCategory();
        
        // Create test data for pagination
        for ($i = 0; $i < 25; $i++) {
            createActiveAuction([
                'title' => "Test Auction {$i}",
                'category_id' => $this->category->id,
                'starting_bid' => 100 + $i,
            ]);
        }
    });

    describe('Auction List Views', function () {
        test('auction index page displays paginated results', function () {
            $response = $this->get('/auctions');
            assertInertiaResponse($response, 'Auctions/Index');
            
            $props = $response->json('props');
            expect($props['auctions'])->toHaveKey('data');
            expect($props['auctions'])->toHaveKey('current_page');
            expect($props['auctions'])->toHaveKey('last_page');
            expect($props['auctions'])->toHaveKey('per_page');
            expect($props['auctions'])->toHaveKey('total');
            
            // Should have items on first page
            expect(count($props['auctions']['data']))->toBeGreaterThan(0);
        });

        test('auction pagination works correctly', function () {
            $page1Response = $this->get('/auctions?page=1');
            $page2Response = $this->get('/auctions?page=2');
            
            $page1Data = $page1Response->json('props.auctions.data');
            $page2Data = $page2Response->json('props.auctions.data');
            
            // Pages should have different content
            expect($page1Data[0]['id'])->not->toBe($page2Data[0]['id']);
        });

        test('auction list shows correct item count per page', function () {
            $response = $this->get('/auctions');
            $auctions = $response->json('props.auctions');
            
            $perPage = $auctions['per_page'];
            $dataCount = count($auctions['data']);
            
            // Should not exceed per_page limit
            expect($dataCount)->toBeLessThanOrEqual($perPage);
        });

        test('featured auctions page displays correctly', function () {
            // Create some featured auctions
            for ($i = 0; $i < 5; $i++) {
                createFeaturedAuction([
                    'title' => "Featured Auction {$i}",
                    'category_id' => $this->category->id,
                ]);
            }

            $response = $this->get('/auctions/featured');
            assertInertiaResponse($response, 'Auctions/Featured');
            
            $auctions = $response->json('props.auctions.data');
            foreach ($auctions as $auction) {
                expect($auction['is_featured'])->toBe(true);
            }
        });

        test('ending soon auctions page displays correctly', function () {
            // Create auctions ending soon
            for ($i = 0; $i < 3; $i++) {
                createEndingSoonAuction([
                    'title' => "Ending Soon {$i}",
                    'category_id' => $this->category->id,
                ]);
            }

            $response = $this->get('/auctions/ending-soon');
            assertInertiaResponse($response, 'Auctions/EndingSoon');
            
            $auctions = $response->json('props.auctions.data');
            expect(count($auctions))->toBeGreaterThan(0);
        });

        test('auction list includes required fields', function () {
            $response = $this->get('/auctions');
            $auctions = $response->json('props.auctions.data');
            
            foreach ($auctions as $auction) {
                expect($auction)->toHaveKey('id');
                expect($auction)->toHaveKey('title');
                expect($auction)->toHaveKey('starting_bid');
                expect($auction)->toHaveKey('current_bid');
                expect($auction)->toHaveKey('end_time');
                expect($auction)->toHaveKey('status');
                expect($auction)->toHaveKey('category');
                expect($auction)->toHaveKey('user');
            }
        });
    });

    describe('Category List Views', function () {
        test('category index page displays all categories', function () {
            // Create additional categories
            for ($i = 0; $i < 5; $i++) {
                createTestCategory(['name' => "Category {$i}"]);
            }

            $response = $this->get('/categories');
            assertInertiaResponse($response, 'Categories/Index');
            
            $categories = $response->json('props.categories');
            expect(count($categories))->toBeGreaterThan(5);
        });

        test('category shows auction count', function () {
            $response = $this->get('/categories');
            $categories = $response->json('props.categories');
            
            foreach ($categories as $category) {
                expect($category)->toHaveKey('auctions_count');
                expect($category['auctions_count'])->toBeInt();
            }
        });

        test('category auctions page displays paginated results', function () {
            $response = $this->get("/categories/{$this->category->slug}/auctions");
            assertInertiaResponse($response, 'Categories/Auctions');
            
            $props = $response->json('props');
            expect($props['auctions'])->toHaveKey('data');
            expect($props['auctions'])->toHaveKey('current_page');
            expect($props['category']['id'])->toBe($this->category->id);
        });

        test('inactive categories are hidden from public view', function () {
            $inactiveCategory = createTestCategory([
                'name' => 'Inactive Category',
                'is_active' => false,
            ]);

            $response = $this->get('/categories');
            $categories = $response->json('props.categories');
            
            $categoryIds = array_column($categories, 'id');
            expect($categoryIds)->not->toContain($inactiveCategory->id);
        });
    });

    describe('Admin List Views', function () {
        test('admin user list displays paginated results', function () {
            // Create additional users
            for ($i = 0; $i < 15; $i++) {
                createVerifiedUser(['email' => "user{$i}@example.com"]);
            }

            $response = $this->actingAs($this->admin)->get('/admin/users');
            
            if ($response->status() === 200) {
                assertInertiaResponse($response, 'Admin/Users/<USER>');
                
                $props = $response->json('props');
                expect($props['users'])->toHaveKey('data');
                expect($props['users'])->toHaveKey('current_page');
            }
        });

        test('admin auction list displays all auctions', function () {
            $response = $this->actingAs($this->admin)->get('/admin/auctions');
            
            if ($response->status() === 200) {
                assertInertiaResponse($response, 'Admin/Auctions/Index');
                
                $auctions = $response->json('props.auctions.data');
                expect(count($auctions))->toBeGreaterThan(0);
            }
        });

        test('admin lists include management actions', function () {
            $response = $this->actingAs($this->admin)->get('/admin/users');
            
            if ($response->status() === 200) {
                $users = $response->json('props.users.data');
                foreach ($users as $user) {
                    expect($user)->toHaveKey('id');
                    expect($user)->toHaveKey('is_active');
                    expect($user)->toHaveKey('email_verified_at');
                }
            }
        });

        test('regular user cannot access admin lists', function () {
            $adminRoutes = [
                '/admin/users',
                '/admin/auctions',
                '/admin/categories',
            ];

            foreach ($adminRoutes as $route) {
                $response = $this->actingAs($this->user)->get($route);
                assertPageRequiresAdmin($response);
            }
        });
    });

    describe('List View Sorting', function () {
        test('auction list can be sorted by different fields', function () {
            $sortOptions = [
                'title' => 'title',
                'starting_bid' => 'starting_bid',
                'end_time' => 'end_time',
                'created_at' => 'created_at',
            ];

            foreach ($sortOptions as $sortField => $expectedField) {
                $response = $this->get("/auctions?sort={$sortField}");
                assertInertiaResponse($response, 'Auctions/Index');
                
                $auctions = $response->json('props.auctions.data');
                expect(count($auctions))->toBeGreaterThan(0);
            }
        });

        test('auction list supports ascending and descending sort', function () {
            $ascResponse = $this->get('/auctions?sort=starting_bid&direction=asc');
            $descResponse = $this->get('/auctions?sort=starting_bid&direction=desc');
            
            $ascAuctions = $ascResponse->json('props.auctions.data');
            $descAuctions = $descResponse->json('props.auctions.data');
            
            if (count($ascAuctions) > 1 && count($descAuctions) > 1) {
                expect($ascAuctions[0]['starting_bid'])->toBeLessThanOrEqual($ascAuctions[1]['starting_bid']);
                expect($descAuctions[0]['starting_bid'])->toBeGreaterThanOrEqual($descAuctions[1]['starting_bid']);
            }
        });

        test('admin lists support sorting', function () {
            $response = $this->actingAs($this->admin)->get('/admin/users?sort=created_at&direction=desc');
            
            if ($response->status() === 200) {
                $users = $response->json('props.users.data');
                expect(count($users))->toBeGreaterThan(0);
            }
        });
    });

    describe('List View Filtering', function () {
        test('auction list can be filtered by category', function () {
            $response = $this->get("/auctions?category={$this->category->id}");
            assertInertiaResponse($response, 'Auctions/Index');
            
            $auctions = $response->json('props.auctions.data');
            foreach ($auctions as $auction) {
                expect($auction['category']['id'])->toBe($this->category->id);
            }
        });

        test('auction list can be filtered by status', function () {
            $response = $this->get('/auctions?status=active');
            assertInertiaResponse($response, 'Auctions/Index');
            
            $auctions = $response->json('props.auctions.data');
            foreach ($auctions as $auction) {
                expect($auction['status'])->toBe('active');
            }
        });

        test('auction list can be filtered by price range', function () {
            $response = $this->get('/auctions?min_price=100&max_price=200');
            assertInertiaResponse($response, 'Auctions/Index');
            
            $auctions = $response->json('props.auctions.data');
            foreach ($auctions as $auction) {
                expect($auction['starting_bid'])->toBeGreaterThanOrEqual(100);
                expect($auction['starting_bid'])->toBeLessThanOrEqual(200);
            }
        });

        test('admin user list can be filtered by status', function () {
            $response = $this->actingAs($this->admin)->get('/admin/users?status=active');
            
            if ($response->status() === 200) {
                $users = $response->json('props.users.data');
                foreach ($users as $user) {
                    expect($user['is_active'])->toBe(true);
                }
            }
        });
    });

    describe('List View Performance', function () {
        test('list views load within acceptable time', function () {
            $routes = [
                '/auctions',
                '/categories',
                "/categories/{$this->category->slug}/auctions",
            ];

            foreach ($routes as $route) {
                testPageLoadPerformance($route, 3.0);
            }
        });

        test('pagination queries are optimized', function () {
            testDatabaseQueryPerformance(function() {
                $this->get('/auctions?page=2');
            }, 20); // Should not require excessive queries
        });

        test('large datasets are handled efficiently', function () {
            // Create many more auctions
            for ($i = 0; $i < 100; $i++) {
                createActiveAuction(['category_id' => $this->category->id]);
            }

            assertExecutionTimeUnder(function() {
                $this->get('/auctions?page=5');
            }, 3.0);
        });
    });
});
