<?php

declare(strict_types=1);

namespace App\Domain\Auction\ValueObjects;

use App\Domain\Shared\ValueObjects\ValueObject;

class AuctionFilters extends ValueObject
{
    public function __construct(
        public readonly ?string $search = null,
        public readonly ?int $categoryId = null,
        public readonly ?float $minPrice = null,
        public readonly ?float $maxPrice = null,
        public readonly ?string $status = null,
        public readonly ?string $sortBy = null,
        public readonly ?string $sortDirection = null,
        public readonly ?bool $featured = null,
        public readonly ?bool $endingSoon = null,
    ) {}

    public static function fromArray(array $data): self
    {
        return new self(
            search: $data['search'] ?? null,
            categoryId: isset($data['category']) ? (int) $data['category'] : null,
            minPrice: isset($data['min_price']) ? (float) $data['min_price'] : null,
            maxPrice: isset($data['max_price']) ? (float) $data['max_price'] : null,
            status: $data['status'] ?? null,
            sortBy: $data['sort'] ?? null,
            sortDirection: $data['direction'] ?? 'asc',
            featured: isset($data['featured']) ? (bool) $data['featured'] : null,
            endingSoon: isset($data['ending_soon']) ? (bool) $data['ending_soon'] : null,
        );
    }

    public function toArray(): array
    {
        return [
            'search' => $this->search,
            'category' => $this->categoryId,
            'min_price' => $this->minPrice,
            'max_price' => $this->maxPrice,
            'status' => $this->status,
            'sort' => $this->sortBy,
            'direction' => $this->sortDirection,
            'featured' => $this->featured,
            'ending_soon' => $this->endingSoon,
        ];
    }

    public function hasFilters(): bool
    {
        return $this->search !== null
            || $this->categoryId !== null
            || $this->minPrice !== null
            || $this->maxPrice !== null
            || $this->status !== null
            || $this->featured !== null
            || $this->endingSoon !== null;
    }

    public function isEmpty(): bool
    {
        return !$this->hasFilters();
    }
}
