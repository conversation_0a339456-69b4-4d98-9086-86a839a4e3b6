<?php

declare(strict_types=1);

use App\Models\User;

describe('Authentication Flow Tests', function () {
    beforeEach(function () {
        $this->user = createVerifiedUser([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
        ]);
    });

    describe('Login Screen Rendering', function () {
        test('login screen can be rendered', function () {
            $response = $this->get('/login');
            assertInertiaResponse($response, 'Auth/Login');
        });

        test('login screen redirects authenticated users', function () {
            $response = $this->actingAs($this->user)->get('/login');
            expect($response->status())->toBe(302);
            expect($response->headers->get('Location'))->toContain('/dashboard');
        });
    });

    describe('Valid Login Scenarios', function () {
        test('users can authenticate with valid credentials', function () {
            $response = $this->post('/login', [
                'email' => $this->user->email,
                'password' => 'password123',
            ]);

            $this->assertAuthenticated();
            $response->assertRedirect(route('dashboard', absolute: false));
        });

        test('users can authenticate with email case insensitive', function () {
            $response = $this->post('/login', [
                'email' => strtoupper($this->user->email),
                'password' => 'password123',
            ]);

            $this->assertAuthenticated();
        });

        test('remember me functionality works', function () {
            $response = $this->post('/login', [
                'email' => $this->user->email,
                'password' => 'password123',
                'remember' => true,
            ]);

            $this->assertAuthenticated();
            // Check that remember token is set
            expect($this->user->fresh()->remember_token)->not->toBeNull();
        });
    });

    describe('Invalid Login Scenarios', function () {
        test('users cannot authenticate with invalid password', function () {
            $response = $this->post('/login', [
                'email' => $this->user->email,
                'password' => 'wrong-password',
            ]);

            $this->assertGuest();
            expect($response->status())->toBe(302);
        });

        test('users cannot authenticate with invalid email', function () {
            $response = $this->post('/login', [
                'email' => '<EMAIL>',
                'password' => 'password123',
            ]);

            $this->assertGuest();
        });

        test('users cannot authenticate with empty credentials', function () {
            $response = $this->post('/login', [
                'email' => '',
                'password' => '',
            ]);

            $this->assertGuest();
            assertValidationErrors($response, ['email', 'password']);
        });

        test('inactive users cannot authenticate', function () {
            $inactiveUser = createInactiveUser([
                'email' => '<EMAIL>',
                'password' => bcrypt('password123'),
            ]);

            $response = $this->post('/login', [
                'email' => $inactiveUser->email,
                'password' => 'password123',
            ]);

            $this->assertGuest();
        });

        test('unverified users are redirected to verification', function () {
            $unverifiedUser = createUnverifiedUser([
                'email' => '<EMAIL>',
                'password' => bcrypt('password123'),
            ]);

            $response = $this->post('/login', [
                'email' => $unverifiedUser->email,
                'password' => 'password123',
            ]);

            expect($response->status())->toBe(302);
            expect($response->headers->get('Location'))->toContain('/verify-email');
        });
    });

    describe('Logout Functionality', function () {
        test('authenticated users can logout', function () {
            $response = $this->actingAs($this->user)->post('/logout');

            $this->assertGuest();
            $response->assertRedirect('/');
        });

        test('logout clears remember token', function () {
            // Set remember token
            $this->user->setRememberToken('test-token');
            $this->user->save();

            $this->actingAs($this->user)->post('/logout');

            expect($this->user->fresh()->remember_token)->toBeNull();
        });

        test('guests cannot access logout', function () {
            $response = $this->post('/logout');
            expect($response->status())->toBe(302);
        });
    });

    describe('Rate Limiting', function () {
        test('login attempts are rate limited', function () {
            // Attempt multiple failed logins
            for ($i = 0; $i < 6; $i++) {
                $this->post('/login', [
                    'email' => $this->user->email,
                    'password' => 'wrong-password',
                ]);
            }

            // Next attempt should be rate limited
            $response = $this->post('/login', [
                'email' => $this->user->email,
                'password' => 'password123',
            ]);

            expect($response->status())->toBe(429);
        });
    });
});
