<?php

declare(strict_types=1);

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;

describe('Inertia Props Validation Tests', function () {
    beforeEach(function () {
        $this->user = createVerifiedUser();
        $this->admin = createAdminUser();
        $this->category = createTestCategory();
        $this->auction = createActiveAuction([
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
        ]);
    });

    describe('Global Inertia Props', function () {
        test('all pages include required global props', function () {
            $response = $this->get('/');
            assertInertiaResponse($response, 'Home');
            
            $props = $response->json('props');
            
            // Global props that should be on every page
            expect($props)->toHaveKey('auth');
            expect($props)->toHaveKey('csrf_token');
            expect($props)->toHaveKey('flash');
            expect($props)->toHaveKey('errors');
            expect($props)->toHaveKey('app');
            
            // App configuration
            expect($props['app'])->toHaveKey('name');
            expect($props['app'])->toHaveKey('url');
            expect($props['app'])->toHaveKey('locale');
            expect($props['app'])->toHaveKey('timezone');
        });

        test('auth prop structure is consistent', function () {
            // Test as guest
            $guestResponse = $this->get('/');
            $guestProps = $guestResponse->json('props');
            
            expect($guestProps['auth'])->toHaveKey('user');
            expect($guestProps['auth']['user'])->toBeNull();
            
            // Test as authenticated user
            $userResponse = $this->actingAs($this->user)->get('/');
            $userProps = $userResponse->json('props');
            
            expect($userProps['auth'])->toHaveKey('user');
            expect($userProps['auth']['user'])->not->toBeNull();
            expect($userProps['auth']['user'])->toHaveKey('id');
            expect($userProps['auth']['user'])->toHaveKey('name');
            expect($userProps['auth']['user'])->toHaveKey('email');
            expect($userProps['auth']['user'])->toHaveKey('avatar');
            expect($userProps['auth']['user'])->toHaveKey('is_admin');
        });

        test('flash messages structure is consistent', function () {
            $response = $this->get('/');
            $props = $response->json('props');
            
            expect($props['flash'])->toHaveKey('success');
            expect($props['flash'])->toHaveKey('error');
            expect($props['flash'])->toHaveKey('warning');
            expect($props['flash'])->toHaveKey('info');
            
            // Should be null or string
            foreach (['success', 'error', 'warning', 'info'] as $type) {
                expect($props['flash'][$type])->toBeOneOf([null, 'string']);
            }
        });

        test('errors prop is always an object', function () {
            $response = $this->get('/');
            $props = $response->json('props');
            
            expect($props['errors'])->toBeArray();
        });
    });

    describe('Home Page Props', function () {
        test('home page includes required props', function () {
            $response = $this->get('/');
            assertInertiaResponse($response, 'Home');
            
            $props = $response->json('props');
            expect($props)->toHaveKey('featured_auctions');
            expect($props)->toHaveKey('ending_soon_auctions');
            expect($props)->toHaveKey('categories');
            expect($props)->toHaveKey('statistics');
            
            // Validate featured auctions structure
            expect($props['featured_auctions'])->toBeArray();
            foreach ($props['featured_auctions'] as $auction) {
                expect($auction)->toHaveKey('id');
                expect($auction)->toHaveKey('title');
                expect($auction)->toHaveKey('current_bid');
                expect($auction)->toHaveKey('end_time');
                expect($auction)->toHaveKey('images');
                expect($auction)->toHaveKey('user');
                expect($auction['user'])->toHaveKey('avatar');
            }
        });

        test('home page statistics are properly formatted', function () {
            $response = $this->get('/');
            $props = $response->json('props');
            
            expect($props['statistics'])->toHaveKey('total_auctions');
            expect($props['statistics'])->toHaveKey('active_auctions');
            expect($props['statistics'])->toHaveKey('total_users');
            expect($props['statistics'])->toHaveKey('total_bids');
            
            foreach ($props['statistics'] as $stat) {
                expect($stat)->toBeNumeric();
            }
        });
    });

    describe('Auction Pages Props', function () {
        test('auction index page includes required props', function () {
            $response = $this->get('/auctions');
            assertInertiaResponse($response, 'Auctions/Index');
            
            $props = $response->json('props');
            expect($props)->toHaveKey('auctions');
            expect($props)->toHaveKey('categories');
            expect($props)->toHaveKey('filters');
            expect($props)->toHaveKey('sort_options');
            
            // Validate pagination structure
            expect($props['auctions'])->toHaveKey('data');
            expect($props['auctions'])->toHaveKey('current_page');
            expect($props['auctions'])->toHaveKey('last_page');
            expect($props['auctions'])->toHaveKey('links');
        });

        test('auction detail page includes complete props', function () {
            $response = $this->get("/auctions/{$this->auction->id}");
            assertInertiaResponse($response, 'Auctions/Show');
            
            $props = $response->json('props');
            expect($props)->toHaveKey('auction');
            expect($props)->toHaveKey('related_auctions');
            expect($props)->toHaveKey('recent_bids');
            expect($props)->toHaveKey('can_bid');
            expect($props)->toHaveKey('can_edit');
            expect($props)->toHaveKey('breadcrumbs');
            
            // Validate auction structure
            $auction = $props['auction'];
            expect($auction)->toHaveKey('id');
            expect($auction)->toHaveKey('title');
            expect($auction)->toHaveKey('description');
            expect($auction)->toHaveKey('images');
            expect($auction)->toHaveKey('user');
            expect($auction)->toHaveKey('category');
            expect($auction)->toHaveKey('time_remaining');
            
            // User should have avatar
            expect($auction['user'])->toHaveKey('avatar');
        });

        test('auction create page includes required props', function () {
            $response = $this->actingAs($this->user)->get('/auctions/create');
            assertInertiaResponse($response, 'Auctions/Create');
            
            $props = $response->json('props');
            expect($props)->toHaveKey('categories');
            expect($props)->toHaveKey('conditions');
            expect($props)->toHaveKey('countries');
            expect($props)->toHaveKey('max_images');
            expect($props)->toHaveKey('allowed_image_types');
            
            // Categories should be properly formatted
            foreach ($props['categories'] as $category) {
                expect($category)->toHaveKey('id');
                expect($category)->toHaveKey('name');
                expect($category)->toHaveKey('slug');
            }
        });

        test('auction edit page includes required props', function () {
            $response = $this->actingAs($this->user)->get("/auctions/{$this->auction->id}/edit");
            
            if ($response->status() === 200) {
                assertInertiaResponse($response, 'Auctions/Edit');
                
                $props = $response->json('props');
                expect($props)->toHaveKey('auction');
                expect($props)->toHaveKey('categories');
                expect($props)->toHaveKey('conditions');
                expect($props)->toHaveKey('can_edit_critical_fields');
                
                // Auction should include all editable fields
                $auction = $props['auction'];
                expect($auction)->toHaveKey('id');
                expect($auction)->toHaveKey('title');
                expect($auction)->toHaveKey('description');
                expect($auction)->toHaveKey('condition');
                expect($auction)->toHaveKey('location');
                expect($auction)->toHaveKey('images');
            }
        });
    });

    describe('User Dashboard Props', function () {
        test('dashboard includes required user props', function () {
            $response = $this->actingAs($this->user)->get('/dashboard');
            assertInertiaResponse($response, 'Dashboard');
            
            $props = $response->json('props');
            expect($props)->toHaveKey('user');
            expect($props)->toHaveKey('user_auctions');
            expect($props)->toHaveKey('user_bids');
            expect($props)->toHaveKey('watching_auctions');
            expect($props)->toHaveKey('recent_activity');
            expect($props)->toHaveKey('statistics');
            
            // User statistics
            expect($props['statistics'])->toHaveKey('total_auctions');
            expect($props['statistics'])->toHaveKey('active_auctions');
            expect($props['statistics'])->toHaveKey('total_bids');
            expect($props['statistics'])->toHaveKey('won_auctions');
        });

        test('user settings pages include complete user data', function () {
            $response = $this->actingAs($this->user)->get('/settings/profile');
            assertInertiaResponse($response, 'Settings/Profile');
            
            $props = $response->json('props');
            expect($props)->toHaveKey('user');
            expect($props)->toHaveKey('countries');
            expect($props)->toHaveKey('timezones');
            
            // User should include all profile fields
            $user = $props['user'];
            expect($user)->toHaveKey('id');
            expect($user)->toHaveKey('name');
            expect($user)->toHaveKey('email');
            expect($user)->toHaveKey('avatar');
            expect($user)->toHaveKey('phone');
            expect($user)->toHaveKey('address');
            expect($user)->toHaveKey('city');
            expect($user)->toHaveKey('state');
            expect($user)->toHaveKey('zip_code');
            expect($user)->toHaveKey('country');
        });
    });

    describe('Admin Pages Props', function () {
        test('admin dashboard includes management props', function () {
            $response = $this->actingAs($this->admin)->get('/admin');
            
            if ($response->status() === 200) {
                assertInertiaResponse($response, 'Admin/Dashboard');
                
                $props = $response->json('props');
                expect($props)->toHaveKey('statistics');
                expect($props)->toHaveKey('recent_users');
                expect($props)->toHaveKey('recent_auctions');
                expect($props)->toHaveKey('pending_approvals');
                expect($props)->toHaveKey('system_health');
                
                // Statistics should include admin metrics
                expect($props['statistics'])->toHaveKey('total_users');
                expect($props['statistics'])->toHaveKey('active_users');
                expect($props['statistics'])->toHaveKey('total_auctions');
                expect($props['statistics'])->toHaveKey('revenue');
            }
        });

        test('admin user management includes required props', function () {
            $response = $this->actingAs($this->admin)->get('/admin/users');
            
            if ($response->status() === 200) {
                assertInertiaResponse($response, 'Admin/Users/<USER>');
                
                $props = $response->json('props');
                expect($props)->toHaveKey('users');
                expect($props)->toHaveKey('filters');
                expect($props)->toHaveKey('sort_options');
                expect($props)->toHaveKey('bulk_actions');
                
                // Users should include management fields
                foreach ($props['users']['data'] as $user) {
                    expect($user)->toHaveKey('id');
                    expect($user)->toHaveKey('name');
                    expect($user)->toHaveKey('email');
                    expect($user)->toHaveKey('avatar');
                    expect($user)->toHaveKey('is_active');
                    expect($user)->toHaveKey('email_verified_at');
                }
            }
        });
    });

    describe('Error Page Props', function () {
        test('404 pages include helpful props', function () {
            $response = $this->get('/non-existent-page');
            expect($response->status())->toBe(404);
            
            // Should still include basic props without errors
            $content = $response->getContent();
            expect($content)->toBeString();
        });

        test('403 pages include appropriate props', function () {
            $response = $this->actingAs($this->user)->get('/admin');
            expect($response->status())->toBe(403);
            
            // Should handle forbidden access gracefully
            $content = $response->getContent();
            expect($content)->toBeString();
        });
    });

    describe('Props Null Safety', function () {
        test('user avatar is always present in props', function () {
            // Test with user without avatar
            $userWithoutAvatar = createVerifiedUser(['avatar' => null]);
            
            $response = $this->actingAs($userWithoutAvatar)->get('/dashboard');
            $props = $response->json('props');
            
            expect($props['auth']['user'])->toHaveKey('avatar');
            expect($props['auth']['user']['avatar'])->toBeNull();
        });

        test('empty collections are arrays not null', function () {
            // Test page with potentially empty data
            $response = $this->get('/auctions?category=999999');
            $props = $response->json('props');
            
            expect($props['auctions']['data'])->toBeArray();
            // Should be empty array, not null
        });

        test('optional props have default values', function () {
            $response = $this->get('/auctions');
            $props = $response->json('props');
            
            // Search query should be present even if empty
            expect($props)->toHaveKey('search_query');
            expect($props['search_query'])->toBeOneOf([null, 'string']);
            
            // Filters should be present
            expect($props)->toHaveKey('filters');
            expect($props['filters'])->toBeArray();
        });

        test('nested objects handle null values gracefully', function () {
            $response = $this->get("/auctions/{$this->auction->id}");
            $props = $response->json('props');
            
            $auction = $props['auction'];
            
            // Optional fields should have keys even if null
            expect($auction)->toHaveKey('reserve_price');
            expect($auction)->toHaveKey('buy_now_price');
            expect($auction)->toHaveKey('shipping_cost');
        });
    });

    describe('Props Performance', function () {
        test('props are efficiently loaded', function () {
            testDatabaseQueryPerformance(function() {
                $this->get('/auctions');
            }, 15); // Should not require excessive queries for props
        });

        test('large prop datasets are paginated', function () {
            // Create many auctions
            for ($i = 0; $i < 50; $i++) {
                createActiveAuction(['category_id' => $this->category->id]);
            }

            assertExecutionTimeUnder(function() {
                $this->get('/auctions');
            }, 3.0);
        });

        test('admin props handle large datasets efficiently', function () {
            // Create many users
            for ($i = 0; $i < 30; $i++) {
                createVerifiedUser(['email' => "user{$i}@example.com"]);
            }

            assertExecutionTimeUnder(function() {
                $this->actingAs($this->admin)->get('/admin/users');
            }, 3.0);
        });
    });

    describe('Props Security', function () {
        test('sensitive data is not exposed in props', function () {
            $response = $this->get("/auctions/{$this->auction->id}");
            $props = $response->json('props');
            
            $auction = $props['auction'];
            
            // Should not expose sensitive user data
            expect($auction['user'])->not->toHaveKey('password');
            expect($auction['user'])->not->toHaveKey('remember_token');
            expect($auction['user'])->not->toHaveKey('email_verification_token');
        });

        test('admin-only data is not exposed to regular users', function () {
            $response = $this->actingAs($this->user)->get('/auctions');
            $props = $response->json('props');
            
            // Regular users shouldn't see admin fields
            foreach ($props['auctions']['data'] as $auction) {
                expect($auction)->not->toHaveKey('admin_notes');
                expect($auction)->not->toHaveKey('internal_status');
            }
        });
    });
});
