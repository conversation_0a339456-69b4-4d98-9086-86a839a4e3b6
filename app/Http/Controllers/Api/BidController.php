<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Actions\Bid\CancelBidAction;
use App\Actions\Bid\PlaceBidAction;
use App\Domain\Auction\Repositories\BidRepositoryInterface;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\User\ValueObjects\UserId;
use App\Http\Controllers\Controller;
use App\Http\Requests\PlaceBidRequest;
use App\Http\Resources\BidResource;
use App\Http\Resources\BidCollection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class BidController extends Controller
{
    public function __construct(
        private BidRepositoryInterface $bidRepository
    ) {}

    /**
     * Display bids for an auction
     */
    public function index(Request $request, int $auctionId): JsonResponse
    {
        $perPage = min($request->get('per_page', 15), 50);
        $bids = $this->bidRepository->findByAuction(Id::fromString((string) $auctionId), $perPage);

        return response()->json(new BidCollection($bids));
    }

    /**
     * Place a new bid
     */
    public function store(PlaceBidRequest $request, PlaceBidAction $action): JsonResponse
    {
        try {
            $data = $request->validated();
            $data['user_id'] = auth()->id();
            $data['ip_address'] = $request->ip();
            $data['user_agent'] = $request->userAgent();

            $bid = $action->execute($data);

            return response()->json([
                'message' => 'Bid placed successfully',
                'data' => new BidResource($bid),
            ], Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to place bid',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Display the specified bid
     */
    public function show(int $id): JsonResponse
    {
        try {
            $bid = $this->bidRepository->findByIdOrFail(Id::fromString((string) $id));
            $userId = UserId::fromString((string) auth()->id());

            // Check if user can view this bid
            if (!$bid->bidderId()->equals($userId) && !auth()->user()->isAdmin()) {
                return response()->json([
                    'message' => 'Unauthorized',
                ], Response::HTTP_FORBIDDEN);
            }

            return response()->json([
                'data' => new BidResource($bid),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Bid not found',
            ], Response::HTTP_NOT_FOUND);
        }
    }

    /**
     * Cancel a bid
     */
    public function destroy(int $id, CancelBidAction $action): JsonResponse
    {
        try {
            $userId = UserId::fromString((string) auth()->id());
            $bid = $action->execute($id, $userId);

            return response()->json([
                'message' => 'Bid cancelled successfully',
                'data' => new BidResource($bid),
            ]);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'message' => 'Bid not found',
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to cancel bid',
                'error' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Get user's bid history
     */
    public function userBids(Request $request): JsonResponse
    {
        $userId = UserId::fromString((string) auth()->id());
        $perPage = min($request->get('per_page', 15), 50);
        $bids = $this->bidRepository->findByBidder($userId, $perPage);

        return response()->json(new BidCollection($bids));
    }

    /**
     * Get user's winning bids
     */
    public function userWinningBids(Request $request): JsonResponse
    {
        $userId = UserId::fromString((string) auth()->id());
        $perPage = min($request->get('per_page', 15), 50);
        $bids = $this->bidRepository->findUserWinningBids($userId, $perPage);

        return response()->json(new BidCollection($bids));
    }

    /**
     * Get user's outbid bids
     */
    public function userOutbidBids(Request $request): JsonResponse
    {
        $userId = UserId::fromString((string) auth()->id());
        $perPage = min($request->get('per_page', 15), 50);
        $bids = $this->bidRepository->findUserOutbidBids($userId, $perPage);

        return response()->json(new BidCollection($bids));
    }

    /**
     * Get bid statistics for an auction
     */
    public function auctionStatistics(int $auctionId): JsonResponse
    {
        $stats = $this->bidRepository->getBidStatisticsForAuction(Id::fromString((string) $auctionId));

        return response()->json([
            'data' => $stats,
        ]);
    }

    /**
     * Get user's bid statistics
     */
    public function userStatistics(): JsonResponse
    {
        $userId = UserId::fromString((string) auth()->id());
        $stats = $this->bidRepository->getUserBidStatistics($userId);

        return response()->json([
            'data' => $stats,
        ]);
    }

    /**
     * Get highest bid for an auction
     */
    public function highestBid(int $auctionId): JsonResponse
    {
        $bid = $this->bidRepository->findHighestBidForAuction(Id::fromString((string) $auctionId));

        if (!$bid) {
            return response()->json([
                'message' => 'No bids found for this auction',
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'data' => new BidResource($bid),
        ]);
    }

    /**
     * Get winning bid for an auction
     */
    public function winningBid(int $auctionId): JsonResponse
    {
        $bid = $this->bidRepository->findWinningBidForAuction(Id::fromString((string) $auctionId));

        if (!$bid) {
            return response()->json([
                'message' => 'No winning bid found for this auction',
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'data' => new BidResource($bid),
        ]);
    }

    /**
     * Get recent bids for an auction (for real-time updates)
     */
    public function recentBids(int $auctionId, Request $request): JsonResponse
    {
        $minutes = $request->get('minutes', 5);
        $bids = $this->bidRepository->findRecentBidsForAuction(
            Id::fromString((string) $auctionId),
            $minutes
        );

        return response()->json([
            'data' => BidResource::collection($bids),
        ]);
    }
}
