<?php

declare(strict_types=1);

namespace App\Http\Controllers\Web;

use App\Actions\Auction\AddToWatchlistAction;
use App\Actions\Auction\RemoveFromWatchlistAction;
use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\User\ValueObjects\UserId;
use App\Http\Controllers\Controller;
use App\Http\Resources\AuctionResource;
use App\Models\Watchlist;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class WatchlistController extends Controller
{
    public function __construct(
        private AuctionRepositoryInterface $auctionRepository
    ) {}

    /**
     * Display user's watchlist
     */
    public function index(Request $request): Response
    {
        $perPage = min($request->get('per_page', 15), 50);
        $userId = UserId::fromString((string) auth()->id());

        $auctions = $this->auctionRepository->findUserWatched($userId, $perPage);

        return Inertia::render('Watchlist/Index', [
            'auctions' => AuctionResource::collection($auctions),
        ]);
    }

    /**
     * Add auction to watchlist
     */
    public function store(Request $request, AddToWatchlistAction $action): RedirectResponse
    {
        try {
            $request->validate([
                'auction_id' => 'required|integer|exists:auctions,id',
            ]);

            $userId = UserId::fromString((string) auth()->id());
            $action->execute($request->auction_id, $userId);

            return redirect()
                ->back()
                ->with('success', 'Auction added to watchlist!');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Remove auction from watchlist
     */
    public function destroy(int $auctionId, RemoveFromWatchlistAction $action): RedirectResponse
    {
        try {
            $auctionIdObj = Id::fromString((string) $auctionId);

            // First check if auction exists
            $auction = $this->auctionRepository->findById($auctionIdObj);
            if (!$auction) {
                return redirect()
                    ->back()
                    ->withErrors(['error' => 'Auction not found']);
            }

            $userId = UserId::fromString((string) auth()->id());
            $action->execute($auctionId, $userId);

            return redirect()
                ->back()
                ->with('success', 'Auction removed from watchlist!');
        } catch (\InvalidArgumentException $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => 'Auction not found']);
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Check if auction is in user's watchlist (AJAX)
     */
    public function check(int $auctionId): JsonResponse
    {
        try {
            $auctionIdObj = Id::fromString((string) $auctionId);

            // First check if auction exists
            $auction = $this->auctionRepository->findById($auctionIdObj);
            if (!$auction) {
                return response()->json([
                    'message' => 'Auction not found',
                ], 404);
            }

            $userId = UserId::fromString((string) auth()->id());
            $isWatched = Watchlist::where('user_id', $userId->value())
                ->where('auction_id', $auctionId)
                ->exists();

            return response()->json([
                'data' => ['is_watched' => $isWatched],
            ]);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'message' => 'Auction not found',
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to check watchlist status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Toggle auction in watchlist (AJAX)
     */
    public function toggle(
        int $auctionId,
        AddToWatchlistAction $addAction,
        RemoveFromWatchlistAction $removeAction
    ): JsonResponse {
        try {
            $auctionIdObj = Id::fromString((string) $auctionId);

            // First check if auction exists
            $auction = $this->auctionRepository->findById($auctionIdObj);
            if (!$auction) {
                return response()->json([
                    'message' => 'Auction not found',
                ], 404);
            }

            $userId = UserId::fromString((string) auth()->id());
            $isWatched = Watchlist::where('user_id', $userId->value())
                ->where('auction_id', $auctionId)
                ->exists();

            if ($isWatched) {
                $removeAction->execute($auctionId, $userId);
                $message = 'Auction removed from watchlist';
                $newStatus = false;
            } else {
                $addAction->execute($auctionId, $userId);
                $message = 'Auction added to watchlist';
                $newStatus = true;
            }

            return response()->json([
                'message' => $message,
                'data' => ['is_watched' => $newStatus],
            ]);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'message' => 'Auction not found',
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to toggle watchlist',
                'error' => $e->getMessage(),
            ], 400);
        }
    }
}
