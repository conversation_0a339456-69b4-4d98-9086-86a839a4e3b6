<?php

declare(strict_types=1);

use App\Models\User;

describe('Session Management Tests', function () {
    beforeEach(function () {
        $this->user = createVerifiedUser(['email' => '<EMAIL>']);
    });

    describe('Session Creation and Validation', function () {
        test('login creates valid session', function () {
            $response = $this->post('/login', [
                'email' => $this->user->email,
                'password' => 'password',
            ]);

            $this->assertAuthenticated();
            expect(session()->getId())->not->toBeEmpty();
            expect(auth()->id())->toBe($this->user->id);
        });

        test('session persists across requests', function () {
            $this->actingAs($this->user);

            $response1 = $this->get('/dashboard');
            $sessionId1 = session()->getId();

            $response2 = $this->get('/dashboard');
            $sessionId2 = session()->getId();

            expect($sessionId1)->toBe($sessionId2);
            $this->assertAuthenticated();
        });

        test('session contains correct user data', function () {
            $this->actingAs($this->user);

            expect(auth()->user()->id)->toBe($this->user->id);
            expect(auth()->user()->email)->toBe($this->user->email);
            expect(auth()->check())->toBe(true);
        });
    });

    describe('Session Security', function () {
        test('session regenerates on login', function () {
            // Start with a guest session
            $this->get('/');
            $guestSessionId = session()->getId();

            // Login should regenerate session ID
            $this->post('/login', [
                'email' => $this->user->email,
                'password' => 'password',
            ]);

            $authenticatedSessionId = session()->getId();
            expect($authenticatedSessionId)->not->toBe($guestSessionId);
        });

        test('session regenerates on logout', function () {
            $this->actingAs($this->user);
            $authenticatedSessionId = session()->getId();

            $this->post('/logout');

            $this->get('/');
            $guestSessionId = session()->getId();
            expect($guestSessionId)->not->toBe($authenticatedSessionId);
        });

        test('session is invalidated on logout', function () {
            $this->actingAs($this->user);
            $this->assertAuthenticated();

            $this->post('/logout');

            $this->assertGuest();
            expect(auth()->check())->toBe(false);
        });

        test('session prevents fixation attacks', function () {
            // Simulate session fixation attempt
            $maliciousSessionId = 'malicious_session_id';
            session()->setId($maliciousSessionId);

            $response = $this->post('/login', [
                'email' => $this->user->email,
                'password' => 'password',
            ]);

            // Session ID should be different after login
            expect(session()->getId())->not->toBe($maliciousSessionId);
        });
    });

    describe('Remember Me Functionality', function () {
        test('remember me sets remember token', function () {
            $response = $this->post('/login', [
                'email' => $this->user->email,
                'password' => 'password',
                'remember' => true,
            ]);

            $this->user->refresh();
            expect($this->user->remember_token)->not->toBeNull();
        });

        test('remember me persists authentication', function () {
            // Login with remember me
            $this->post('/login', [
                'email' => $this->user->email,
                'password' => 'password',
                'remember' => true,
            ]);

            // Simulate session expiry by clearing session
            session()->flush();

            // User should still be authenticated via remember token
            $response = $this->get('/dashboard');
            $this->assertAuthenticated();
        });

        test('logout clears remember token', function () {
            // Login with remember me
            $this->post('/login', [
                'email' => $this->user->email,
                'password' => 'password',
                'remember' => true,
            ]);

            $this->user->refresh();
            expect($this->user->remember_token)->not->toBeNull();

            // Logout should clear remember token
            $this->post('/logout');

            $this->user->refresh();
            expect($this->user->remember_token)->toBeNull();
        });
    });

    describe('Session Timeout and Expiry', function () {
        test('session expires after configured lifetime', function () {
            $this->actingAs($this->user);

            // Simulate session expiry by manipulating session data
            session()->put('login_time', now()->subHours(3)->timestamp);

            $response = $this->get('/dashboard');
            
            // Should redirect to login if session expired
            if (config('session.lifetime') < 180) { // 3 hours in minutes
                assertPageRequiresAuth($response);
            } else {
                assertPageLoadsSuccessfully($response);
            }
        });

        test('session activity updates last activity time', function () {
            $this->actingAs($this->user);

            $initialTime = session()->get('last_activity', 0);
            sleep(1);

            $this->get('/dashboard');

            $updatedTime = session()->get('last_activity', 0);
            expect($updatedTime)->toBeGreaterThan($initialTime);
        });
    });

    describe('Concurrent Sessions', function () {
        test('user can have multiple browser sessions', function () {
            // First session
            $response1 = $this->post('/login', [
                'email' => $this->user->email,
                'password' => 'password',
            ]);
            $sessionId1 = session()->getId();

            // Simulate second browser/device
            session()->flush();
            session()->regenerate();

            // Second session
            $response2 = $this->post('/login', [
                'email' => $this->user->email,
                'password' => 'password',
            ]);
            $sessionId2 = session()->getId();

            expect($sessionId1)->not->toBe($sessionId2);
        });

        test('logout only affects current session', function () {
            // This test would require more complex session management
            // to track multiple sessions per user
            $this->markTestSkipped('Multiple session tracking requires custom implementation');
        });
    });

    describe('Session Data Integrity', function () {
        test('session data is properly encrypted', function () {
            $this->actingAs($this->user);
            session()->put('sensitive_data', 'secret_value');

            // Check that session data is encrypted in storage
            $sessionId = session()->getId();
            $sessionFile = storage_path("framework/sessions/{$sessionId}");

            if (file_exists($sessionFile)) {
                $sessionContent = file_get_contents($sessionFile);
                expect($sessionContent)->not->toContain('secret_value');
            }
        });

        test('session prevents tampering', function () {
            $this->actingAs($this->user);

            // Attempt to tamper with session data
            session()->put('user_id', 999);

            // Authentication should still work correctly
            expect(auth()->id())->toBe($this->user->id);
            expect(auth()->id())->not->toBe(999);
        });

        test('session validates CSRF tokens', function () {
            $this->actingAs($this->user);

            // Valid CSRF token should work
            $response = $this->post('/settings/profile', [
                '_token' => csrf_token(),
                'name' => 'Updated Name',
            ]);

            expect($response->status())->not->toBe(419);

            // Invalid CSRF token should fail
            $response = $this->post('/settings/profile', [
                '_token' => 'invalid_token',
                'name' => 'Updated Name',
            ]);

            expect($response->status())->toBe(419);
        });
    });

    describe('Session Cleanup', function () {
        test('expired sessions are cleaned up', function () {
            // This would test the session garbage collection
            $this->markTestSkipped('Session cleanup testing requires custom implementation');
        });

        test('user deactivation invalidates sessions', function () {
            $this->actingAs($this->user);
            $this->assertAuthenticated();

            // Deactivate user
            $this->user->update(['is_active' => false]);

            // Next request should require re-authentication
            $response = $this->get('/dashboard');
            assertPageRequiresAuth($response);
        });
    });
});
