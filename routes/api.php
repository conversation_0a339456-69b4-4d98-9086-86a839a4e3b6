<?php

use App\Http\Controllers\Api\AuctionController;
use App\Http\Controllers\Api\BidController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\WatchlistController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::prefix('v1')->group(function () {
    // Authentication routes
    Route::post('/register', [UserController::class, 'register']);
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('/reset-password', [AuthController::class, 'resetPassword']);

    // Public auction routes
    Route::get('/auctions', [AuctionController::class, 'index']);
    Route::get('/auctions/featured', [AuctionController::class, 'featured']);
    Route::get('/auctions/ending-soon', [AuctionController::class, 'endingSoon']);
    Route::get('/auctions/{id}', [AuctionController::class, 'show'])->where('id', '[0-9]+');
    Route::get('/auctions/{id}/bids', [BidController::class, 'index'])->where('id', '[0-9]+');
    Route::get('/auctions/{id}/statistics', [BidController::class, 'auctionStatistics'])->where('id', '[0-9]+');

    // Public category routes
    Route::get('/categories', [CategoryController::class, 'index']);
    Route::get('/categories/{id}', [CategoryController::class, 'show'])->where('id', '[0-9]+');
    Route::get('/categories/{id}/auctions', [CategoryController::class, 'auctions'])->where('id', '[0-9]+');

    // Platform statistics
    Route::get('/statistics', [AuctionController::class, 'statistics']);

    // Payment webhook (no auth required)
    Route::post('/payments/webhook', [PaymentController::class, 'webhook']);
});

// Protected routes
Route::prefix('v1')->middleware(['auth:sanctum'])->group(function () {
    // User routes
    Route::get('/user/profile', [UserController::class, 'profile']);
    Route::put('/user/profile', [UserController::class, 'updateProfile']);
    Route::put('/user/password', [UserController::class, 'changePassword']);
    Route::delete('/user/account', [UserController::class, 'deleteAccount']);
    Route::get('/user/statistics', [UserController::class, 'statistics']);
    Route::get('/user/dashboard', [UserController::class, 'dashboard']);

    // User notifications
    Route::get('/user/notifications', [UserController::class, 'notifications']);
    Route::put('/user/notifications/{id}/read', [UserController::class, 'markNotificationRead']);
    Route::put('/user/notifications/read-all', [UserController::class, 'markAllNotificationsRead']);
    Route::get('/user/notifications/unread-count', [UserController::class, 'unreadNotificationsCount']);

    // Auction management
    Route::post('/auctions', [AuctionController::class, 'store']);
    Route::put('/auctions/{id}', [AuctionController::class, 'update'])->where('id', '[0-9]+');
    Route::delete('/auctions/{id}', [AuctionController::class, 'destroy'])->where('id', '[0-9]+');
    Route::post('/auctions/{id}/activate', [AuctionController::class, 'activate'])->where('id', '[0-9]+');
    Route::post('/auctions/{id}/end', [AuctionController::class, 'end'])->where('id', '[0-9]+');
    Route::post('/auctions/{id}/images', [AuctionController::class, 'uploadImages'])->where('id', '[0-9]+');

    // User's auction lists
    Route::get('/user/auctions', [AuctionController::class, 'userAuctions']);
    Route::get('/user/watched-auctions', [AuctionController::class, 'watchedAuctions']);
    Route::get('/user/won-auctions', [AuctionController::class, 'wonAuctions']);

    // Bidding
    Route::post('/bids', [BidController::class, 'store']);
    Route::get('/bids/{id}', [BidController::class, 'show'])->where('id', '[0-9]+');
    Route::delete('/bids/{id}', [BidController::class, 'destroy'])->where('id', '[0-9]+');
    Route::get('/user/bids', [BidController::class, 'userBids']);
    Route::get('/user/winning-bids', [BidController::class, 'userWinningBids']);
    Route::get('/user/outbid-bids', [BidController::class, 'userOutbidBids']);
    Route::get('/user/bid-statistics', [BidController::class, 'userStatistics']);

    // Bid information
    Route::get('/auctions/{id}/highest-bid', [BidController::class, 'highestBid'])->where('id', '[0-9]+');
    Route::get('/auctions/{id}/winning-bid', [BidController::class, 'winningBid'])->where('id', '[0-9]+');
    Route::get('/auctions/{id}/recent-bids', [BidController::class, 'recentBids'])->where('id', '[0-9]+');

    // Watchlist
    Route::post('/watchlist', [WatchlistController::class, 'store']);
    Route::delete('/watchlist/{auctionId}', [WatchlistController::class, 'destroy'])->where('auctionId', '[0-9]+');
    Route::get('/watchlist', [WatchlistController::class, 'index']);

    // Payments
    Route::get('/payments', [PaymentController::class, 'index']);
    Route::post('/payments', [PaymentController::class, 'store']);
    Route::get('/payments/{id}', [PaymentController::class, 'show'])->where('id', '[0-9]+');
    Route::get('/user/payment-statistics', [PaymentController::class, 'userStatistics']);

    // Payment processing
    Route::post('/payments/create-intent', [PaymentController::class, 'createPaymentIntent']);
    Route::post('/payments/confirm-intent', [PaymentController::class, 'confirmPaymentIntent']);
    Route::get('/payments/methods', [PaymentController::class, 'paymentMethods']);
    Route::post('/payments/methods', [PaymentController::class, 'addPaymentMethod']);
    Route::delete('/payments/methods/{id}', [PaymentController::class, 'removePaymentMethod'])->where('id', '[0-9]+');

    // Search
    Route::get('/search', [SearchController::class, 'search']);
    Route::get('/search/suggestions', [SearchController::class, 'suggestions']);
});

// Admin routes
Route::prefix('v1/admin')->middleware(['auth:sanctum', 'admin'])->group(function () {
    // User management
    Route::get('/users', [AdminUserController::class, 'index']);
    Route::get('/users/{id}', [AdminUserController::class, 'show']);
    Route::put('/users/{id}', [AdminUserController::class, 'update']);
    Route::delete('/users/{id}', [AdminUserController::class, 'destroy']);
    Route::post('/users/{id}/verify', [AdminUserController::class, 'verify']);
    Route::post('/users/{id}/suspend', [AdminUserController::class, 'suspend']);
    Route::post('/users/{id}/unsuspend', [AdminUserController::class, 'unsuspend']);

    // Auction management
    Route::get('/auctions', [AdminAuctionController::class, 'index']);
    Route::put('/auctions/{id}', [AdminAuctionController::class, 'update']);
    Route::delete('/auctions/{id}', [AdminAuctionController::class, 'destroy']);
    Route::post('/auctions/{id}/feature', [AdminAuctionController::class, 'feature']);
    Route::post('/auctions/{id}/unfeature', [AdminAuctionController::class, 'unfeature']);
    Route::post('/auctions/{id}/end', [AdminAuctionController::class, 'end']);

    // Category management
    Route::post('/categories', [CategoryController::class, 'store']);
    Route::put('/categories/{id}', [CategoryController::class, 'update']);
    Route::delete('/categories/{id}', [CategoryController::class, 'destroy']);
    Route::post('/categories/reorder', [CategoryController::class, 'reorder']);

    // Payment management
    Route::get('/payments', [AdminPaymentController::class, 'index']);
    Route::get('/payments/{id}', [AdminPaymentController::class, 'show']);
    Route::post('/payments/{id}/refund', [PaymentController::class, 'refund']);
    Route::get('/payments/statistics', [PaymentController::class, 'statistics']);

    // Platform statistics and reports
    Route::get('/statistics/overview', [AdminStatisticsController::class, 'overview']);
    Route::get('/statistics/revenue', [AdminStatisticsController::class, 'revenue']);
    Route::get('/statistics/users', [AdminStatisticsController::class, 'users']);
    Route::get('/statistics/auctions', [AdminStatisticsController::class, 'auctions']);
    Route::get('/reports/export', [AdminReportsController::class, 'export']);

    // System maintenance
    Route::post('/maintenance/cleanup-expired', [AdminMaintenanceController::class, 'cleanupExpired']);
    Route::post('/maintenance/process-payments', [AdminMaintenanceController::class, 'processPayments']);
    Route::post('/maintenance/send-notifications', [AdminMaintenanceController::class, 'sendNotifications']);
});

// Rate limited routes
Route::middleware(['throttle:60,1'])->group(function () {
    Route::post('/v1/bids', [BidController::class, 'store']);
    Route::post('/v1/payments/create-intent', [PaymentController::class, 'createPaymentIntent']);
});

// Real-time routes (higher rate limits)
Route::middleware(['throttle:120,1'])->group(function () {
    Route::get('/v1/auctions/{id}/recent-bids', [BidController::class, 'recentBids']);
    Route::get('/v1/user/notifications/unread-count', [UserController::class, 'unreadNotificationsCount']);
});
