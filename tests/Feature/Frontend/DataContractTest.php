<?php

declare(strict_types=1);

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;

describe('Frontend Data Contract Tests', function () {
    beforeEach(function () {
        $this->user = createVerifiedUser();
        $this->admin = createAdminUser();
        $this->category = createTestCategory();
        $this->auction = createActiveAuction([
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
        ]);
    });

    describe('User Data Structure Validation', function () {
        test('user objects always include avatar field', function () {
            $response = $this->get('/auctions');

            $response->assertStatus(200)
                    ->assertInertia(fn ($page) => $page
                        ->component('Auctions/Index')
                        ->has('auctions.data')
                        ->has('auctions.data.0.seller.avatar')
                    );
        });

        test('user profile page includes all required user fields', function () {
            $response = $this->actingAs($this->user)->withHeaders([
                'X-Inertia' => 'true',
                'X-Inertia-Version' => '1.0',
            ])->get('/settings/profile');
            assertInertiaResponse($response, 'Settings/Profile');

            $user = $response->json('props.user');
            expect($user)->toHaveKey('id');
            expect($user)->toHaveKey('name');
            expect($user)->toHaveKey('email');
            expect($user)->toHaveKey('avatar');
            expect($user)->toHaveKey('phone');
            expect($user)->toHaveKey('address');
            expect($user)->toHaveKey('city');
            expect($user)->toHaveKey('state');
            expect($user)->toHaveKey('zip_code');
            expect($user)->toHaveKey('country');
        });

        test('auction owner data includes required fields', function () {
            // Debug: Check what auction we're using
            expect($this->auction->id)->toBeInt();
            expect($this->auction->user_id)->toBeInt();

            $response = $this->get("/auctions/{$this->auction->id}");

            $response->assertStatus(200)
                    ->assertInertia(fn ($page) => $page
                        ->component('Auctions/Show')
                        ->has('auction')
                        ->dump('auction.seller') // Check seller specifically
                    );
        });

        test('bid user data includes required fields', function () {
            $bid = createTestBid([
                'auction_id' => $this->auction->id,
                'user_id' => $this->user->id,
            ]);

            $response = $this->get("/auctions/{$this->auction->id}/bids");

            if ($response->status() === 200) {
                $bids = $response->json('data') ?? $response->json();
                foreach ($bids as $bid) {
                    expect($bid['user'])->toHaveKey('id');
                    expect($bid['user'])->toHaveKey('name');
                    expect($bid['user'])->toHaveKey('avatar');

                    // Email should be hidden for privacy
                    expect($bid['user'])->not->toHaveKey('email');
                }
            }
        });

        test('admin user list includes management fields', function () {
            $response = $this->actingAs($this->admin)->get('/admin/users');

            if ($response->status() === 200) {
                $users = $response->json('props.users.data');
                foreach ($users as $user) {
                    expect($user)->toHaveKey('id');
                    expect($user)->toHaveKey('name');
                    expect($user)->toHaveKey('email');
                    expect($user)->toHaveKey('avatar');
                    expect($user)->toHaveKey('is_active');
                    expect($user)->toHaveKey('email_verified_at');
                    expect($user)->toHaveKey('created_at');
                }
            }
        });
    });

    describe('Auction Data Structure Validation', function () {
        test('auction list includes all required fields', function () {
            $response = $this->get('/auctions');

            $response->assertStatus(200)
                    ->assertInertia(fn ($page) => $page
                        ->component('Auctions/Index')
                        ->has('auctions.data')
                        ->has('auctions.data.0.id')
                        ->has('auctions.data.0.title')
                        ->has('auctions.data.0.description')
                        ->has('auctions.data.0.starting_price')
                        ->has('auctions.data.0.current_bid')
                        ->has('auctions.data.0.timing.end_time')
                        ->has('auctions.data.0.status')
                        ->has('auctions.data.0.features.is_featured')
                        ->has('auctions.data.0.statistics.views_count')
                        ->has('auctions.data.0.statistics.bids_count')
                        ->has('auctions.data.0.seller')
                        ->has('auctions.data.0.category')
                        ->has('auctions.data.0.metadata.created_at')
                    );
        });

        test('auction detail page includes complete auction data', function () {
            $response = getInertia("/auctions/{$this->auction->id}");
            assertInertiaResponse($response, 'Auctions/Show');

            $auction = $response->json('props.auction');
            expect($auction)->toHaveKey('id');
            expect($auction)->toHaveKey('title');
            expect($auction)->toHaveKey('description');
            expect($auction)->toHaveKey('condition');
            expect($auction)->toHaveKey('location');
            expect($auction)->toHaveKey('shipping_cost');
            expect($auction)->toHaveKey('starting_bid');
            expect($auction)->toHaveKey('current_bid');
            expect($auction)->toHaveKey('reserve_price');
            expect($auction)->toHaveKey('buy_now_price');
            expect($auction)->toHaveKey('start_time');
            expect($auction)->toHaveKey('end_time');
            expect($auction)->toHaveKey('status');
            expect($auction)->toHaveKey('images');
            expect($auction)->toHaveKey('category');
            expect($auction)->toHaveKey('user');
            expect($auction)->toHaveKey('bids_count');
            expect($auction)->toHaveKey('watchers_count');
        });

        test('auction images array is always present', function () {
            $response = getInertia("/auctions/{$this->auction->id}");
            $auction = $response->json('props.auction');

            expect($auction['images'])->toBeArray();
            // Can be empty array, but must be an array
            foreach ($auction['images'] as $image) {
                expect($image)->toHaveKey('id');
                expect($image)->toHaveKey('url');
                expect($image)->toHaveKey('thumbnail_url');
                expect($image)->toHaveKey('alt_text');
            }
        });

        test('auction category data is complete', function () {
            $response = getInertia("/auctions/{$this->auction->id}");
            $auction = $response->json('props.auction');

            expect($auction['category'])->toHaveKey('id');
            expect($auction['category'])->toHaveKey('name');
            expect($auction['category'])->toHaveKey('slug');
            expect($auction['category'])->toHaveKey('description');
        });
    });

    describe('Category Data Structure Validation', function () {
        test('category list includes required fields', function () {
            $response = $this->get('/categories');
            assertInertiaResponse($response, 'Categories/Index');

            $categories = $response->json('props.categories');
            foreach ($categories as $category) {
                expect($category)->toHaveKey('id');
                expect($category)->toHaveKey('name');
                expect($category)->toHaveKey('slug');
                expect($category)->toHaveKey('description');
                expect($category)->toHaveKey('auctions_count');
                expect($category)->toHaveKey('image');
            }
        });

        test('category detail page includes complete data', function () {
            $response = $this->get("/categories/{$this->category->slug}");
            assertInertiaResponse($response, 'Categories/Show');

            $category = $response->json('props.category');
            expect($category)->toHaveKey('id');
            expect($category)->toHaveKey('name');
            expect($category)->toHaveKey('slug');
            expect($category)->toHaveKey('description');
            expect($category)->toHaveKey('image');
            expect($category)->toHaveKey('auctions_count');
            expect($category)->toHaveKey('featured_auctions');
        });
    });

    describe('Dashboard Data Structure Validation', function () {
        test('user dashboard includes all required sections', function () {
            $response = $this->actingAs($this->user)->get('/dashboard');
            assertInertiaResponse($response, 'Dashboard');

            $props = $response->json('props');
            expect($props)->toHaveKey('user');
            expect($props)->toHaveKey('user_auctions');
            expect($props)->toHaveKey('user_bids');
            expect($props)->toHaveKey('watching_auctions');
            expect($props)->toHaveKey('recent_activity');
            expect($props)->toHaveKey('statistics');
        });

        test('admin dashboard includes management data', function () {
            $response = $this->actingAs($this->admin)->get('/admin');

            if ($response->status() === 200) {
                assertInertiaResponse($response, 'Admin/Dashboard');

                $props = $response->json('props');
                expect($props)->toHaveKey('statistics');
                expect($props)->toHaveKey('recent_users');
                expect($props)->toHaveKey('recent_auctions');
                expect($props)->toHaveKey('pending_approvals');
            }
        });
    });

    describe('Pagination Data Structure Validation', function () {
        test('paginated responses include required pagination fields', function () {
            $response = $this->get('/auctions');
            $auctions = $response->json('props.auctions');

            expect($auctions)->toHaveKey('data');
            expect($auctions)->toHaveKey('current_page');
            expect($auctions)->toHaveKey('last_page');
            expect($auctions)->toHaveKey('per_page');
            expect($auctions)->toHaveKey('total');
            expect($auctions)->toHaveKey('from');
            expect($auctions)->toHaveKey('to');
            expect($auctions)->toHaveKey('links');
        });

        test('pagination links are properly formatted', function () {
            $response = $this->get('/auctions');
            $links = $response->json('props.auctions.links');

            foreach ($links as $link) {
                expect($link)->toHaveKey('url');
                expect($link)->toHaveKey('label');
                expect($link)->toHaveKey('active');
            }
        });
    });

    describe('Error State Data Validation', function () {
        test('validation errors include proper structure', function () {
            $response = $this->actingAs($this->user)->post('/auctions', [
                'title' => '', // Invalid data
            ]);

            if ($response->status() === 422) {
                $errors = $response->json('errors');
                expect($errors)->toBeArray();

                foreach ($errors as $field => $messages) {
                    expect($messages)->toBeArray();
                    foreach ($messages as $message) {
                        expect($message)->toBeString();
                    }
                }
            }
        });

        test('404 pages include proper error data', function () {
            $response = $this->get('/auctions/999999');
            expect($response->status())->toBe(404);

            // Should not cause JS errors even on 404
            $content = $response->getContent();
            expect($content)->toBeString();
        });
    });

    describe('Null Value Handling', function () {
        test('optional fields handle null values gracefully', function () {
            // Create user without avatar
            $userWithoutAvatar = createVerifiedUser([
                'avatar' => null,
            ]);

            $auction = createActiveAuction([
                'user_id' => $userWithoutAvatar->id,
                'category_id' => $this->category->id,
            ]);

            $response = $this->get("/auctions/{$auction->id}");
            $auctionData = $response->json('props.auction');

            // Avatar should be null, not missing
            expect($auctionData['user'])->toHaveKey('avatar');
            expect($auctionData['user']['avatar'])->toBeNull();
        });

        test('empty arrays are provided instead of null for collections', function () {
            // Create auction without images
            $response = $this->get("/auctions/{$this->auction->id}");
            $auction = $response->json('props.auction');

            expect($auction['images'])->toBeArray();
            // Even if empty, should be array not null
        });

        test('numeric fields have proper defaults', function () {
            $response = $this->get("/auctions/{$this->auction->id}");
            $auction = $response->json('props.auction');

            expect($auction['current_bid'])->toBeNumeric();
            expect($auction['view_count'])->toBeNumeric();
            expect($auction['bids_count'])->toBeNumeric();
            expect($auction['watchers_count'])->toBeNumeric();
        });
    });
});
