<?php

declare(strict_types=1);

use App\Models\User;

describe('Error Pages Tests', function () {
    beforeEach(function () {
        $this->user = createVerifiedUser();
        $this->admin = createAdminUser();
    });

    describe('404 Not Found Errors', function () {
        test('non-existent routes return 404', function () {
            $nonExistentRoutes = [
                '/non-existent-page',
                '/auctions/999999',
                '/categories/non-existent',
                '/admin/non-existent',
                '/api/non-existent',
            ];

            foreach ($nonExistentRoutes as $route) {
                $response = $this->get($route);
                expect($response->status())->toBe(404);
            }
        });

        test('404 page renders correctly for web routes', function () {
            $response = $this->get('/non-existent-page');
            expect($response->status())->toBe(404);
            
            // Should render error page, not throw exception
            expect($response->getContent())->toContain('404');
        });

        test('404 API responses return JSON', function () {
            $response = $this->getJson('/api/non-existent');
            expect($response->status())->toBe(404);
            expect($response->json())->toHaveKey('message');
            expect($response->headers->get('Content-Type'))->toContain('application/json');
        });

        test('invalid auction IDs return 404', function () {
            $invalidIds = ['abc', 'null', '-1', '999999'];

            foreach ($invalidIds as $id) {
                $response = $this->get("/auctions/{$id}");
                expect($response->status())->toBe(404);
            }
        });

        test('invalid category slugs return 404', function () {
            $response = $this->get('/categories/non-existent-category');
            expect($response->status())->toBe(404);
        });

        test('deleted resources return 404', function () {
            $auction = createActiveAuction();
            $auctionId = $auction->id;
            
            // Soft delete the auction
            $auction->delete();

            $response = $this->get("/auctions/{$auctionId}");
            expect($response->status())->toBe(404);
        });
    });

    describe('403 Forbidden Errors', function () {
        test('non-admin users get 403 for admin routes', function () {
            $adminRoutes = [
                '/admin',
                '/admin/users',
                '/admin/auctions',
                '/admin/settings',
            ];

            foreach ($adminRoutes as $route) {
                $response = $this->actingAs($this->user)->get($route);
                expect($response->status())->toBe(403);
            }
        });

        test('403 page renders correctly', function () {
            $response = $this->actingAs($this->user)->get('/admin');
            expect($response->status())->toBe(403);
            expect($response->getContent())->toContain('403');
        });

        test('API 403 responses return JSON', function () {
            $response = $this->actingAs($this->user)->getJson('/api/v1/admin/users');
            expect($response->status())->toBe(403);
            expect($response->json())->toHaveKey('message');
        });

        test('users cannot access other users resources', function () {
            $otherUser = createVerifiedUser();
            $otherAuction = createActiveAuction(['user_id' => $otherUser->id]);

            $response = $this->actingAs($this->user)->get("/auctions/{$otherAuction->id}/edit");
            expect($response->status())->toBe(403);
        });

        test('inactive users get 403 for protected routes', function () {
            $inactiveUser = createInactiveUser();

            $response = $this->actingAs($inactiveUser)->get('/dashboard');
            expect($response->status())->toBeIn([403, 302]);
        });
    });

    describe('401 Unauthorized Errors', function () {
        test('guests get 401 for API protected routes', function () {
            $protectedApiRoutes = [
                '/api/v1/user',
                '/api/v1/auctions',
                '/api/v1/bids',
            ];

            foreach ($protectedApiRoutes as $route) {
                $response = $this->getJson($route);
                expect($response->status())->toBe(401);
                expect($response->json())->toHaveKey('message');
            }
        });

        test('expired tokens get 401', function () {
            // This would test API token expiration
            $this->markTestSkipped('Token expiration testing requires API token implementation');
        });

        test('invalid API tokens get 401', function () {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer invalid-token',
            ])->getJson('/api/v1/user');

            expect($response->status())->toBe(401);
        });
    });

    describe('422 Validation Errors', function () {
        test('invalid form data returns 422', function () {
            $response = $this->actingAs($this->user)->post('/auctions', [
                'title' => '', // Required field
                'description' => '',
                'starting_bid' => 'invalid',
            ]);

            expect($response->status())->toBe(422);
            expect($response->json('errors'))->toHaveKey('title');
        });

        test('API validation errors return JSON', function () {
            $response = $this->actingAs($this->user)->postJson('/api/v1/auctions', [
                'title' => '',
                'starting_bid' => 'invalid',
            ]);

            expect($response->status())->toBe(422);
            expect($response->json())->toHaveKey('errors');
        });

        test('validation errors include field-specific messages', function () {
            $response = $this->actingAs($this->user)->post('/auctions', [
                'title' => '',
                'starting_bid' => -10,
                'end_time' => 'invalid-date',
            ]);

            $errors = $response->json('errors');
            expect($errors)->toHaveKey('title');
            expect($errors)->toHaveKey('starting_bid');
            expect($errors)->toHaveKey('end_time');
        });
    });

    describe('500 Server Errors', function () {
        test('database connection errors are handled gracefully', function () {
            // This would require mocking database failures
            $this->markTestSkipped('Database error testing requires custom implementation');
        });

        test('file system errors are handled gracefully', function () {
            // This would require mocking file system failures
            $this->markTestSkipped('File system error testing requires custom implementation');
        });

        test('third-party service errors are handled gracefully', function () {
            // This would require mocking external service failures
            $this->markTestSkipped('External service error testing requires custom implementation');
        });

        test('500 errors render error page', function () {
            // This would require triggering a controlled 500 error
            $this->markTestSkipped('500 error testing requires custom error trigger');
        });
    });

    describe('Rate Limiting Errors', function () {
        test('too many requests return 429', function () {
            // Test rate limiting on login attempts
            for ($i = 0; $i < 6; $i++) {
                $this->post('/login', [
                    'email' => '<EMAIL>',
                    'password' => 'wrong-password',
                ]);
            }

            $response = $this->post('/login', [
                'email' => '<EMAIL>',
                'password' => 'wrong-password',
            ]);

            expect($response->status())->toBe(429);
        });

        test('API rate limiting returns JSON response', function () {
            // Test API rate limiting
            for ($i = 0; $i < 61; $i++) {
                $this->getJson('/api/auctions');
            }

            $response = $this->getJson('/api/auctions');
            expect($response->status())->toBe(429);
            expect($response->json())->toHaveKey('message');
        });
    });

    describe('CSRF Token Errors', function () {
        test('missing CSRF token returns 419', function () {
            $response = $this->post('/settings/profile', [
                'name' => 'Test User',
            ]);

            expect($response->status())->toBe(419);
        });

        test('invalid CSRF token returns 419', function () {
            $response = $this->post('/settings/profile', [
                '_token' => 'invalid-token',
                'name' => 'Test User',
            ]);

            expect($response->status())->toBe(419);
        });

        test('expired CSRF token returns 419', function () {
            // This would require manipulating CSRF token expiration
            $this->markTestSkipped('CSRF expiration testing requires custom implementation');
        });
    });

    describe('Error Page Consistency', function () {
        test('error pages maintain site layout', function () {
            $response = $this->get('/non-existent-page');
            expect($response->status())->toBe(404);
            
            $content = $response->getContent();
            // Should include navigation and footer
            expect($content)->toContain('navigation');
        });

        test('error pages are accessible', function () {
            $response = $this->get('/non-existent-page');
            expect($response->status())->toBe(404);
            
            $content = $response->getContent();
            // Should include proper ARIA labels and semantic HTML
            expect($content)->toContain('role=');
        });

        test('error pages include helpful information', function () {
            $response = $this->get('/non-existent-page');
            expect($response->status())->toBe(404);
            
            $content = $response->getContent();
            // Should include links to help users navigate
            expect($content)->toContain('home');
        });
    });
});
