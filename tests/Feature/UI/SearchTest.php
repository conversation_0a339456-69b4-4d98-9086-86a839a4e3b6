<?php

declare(strict_types=1);

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;

describe('Search Functionality Tests', function () {
    beforeEach(function () {
        $this->user = createVerifiedUser();
        $this->admin = createAdminUser();
        $this->category = createTestCategory(['name' => 'Electronics']);
        
        // Create searchable test data
        $this->searchableAuctions = [
            createActiveAuction([
                'title' => 'iPhone 13 Pro Max',
                'description' => 'Latest Apple smartphone with amazing camera',
                'category_id' => $this->category->id,
            ]),
            createActiveAuction([
                'title' => 'Samsung Galaxy S22',
                'description' => 'Android flagship phone with great display',
                'category_id' => $this->category->id,
            ]),
            createActiveAuction([
                'title' => 'MacBook Pro 2023',
                'description' => 'Professional laptop for developers and designers',
                'category_id' => $this->category->id,
            ]),
        ];
    });

    describe('Basic Search Functionality', function () {
        test('search returns relevant results for title matches', function () {
            $response = $this->get('/auctions?search=iPhone');
            assertInertiaResponse($response, 'Auctions/Index');
            
            $auctions = $response->json('props.auctions.data');
            expect(count($auctions))->toBeGreaterThan(0);
            
            foreach ($auctions as $auction) {
                expect(strtolower($auction['title']))->toContain('iphone');
            }
        });

        test('search returns relevant results for description matches', function () {
            $response = $this->get('/auctions?search=camera');
            assertInertiaResponse($response, 'Auctions/Index');
            
            $auctions = $response->json('props.auctions.data');
            expect(count($auctions))->toBeGreaterThan(0);
            
            $found = false;
            foreach ($auctions as $auction) {
                if (stripos($auction['description'], 'camera') !== false) {
                    $found = true;
                    break;
                }
            }
            expect($found)->toBe(true);
        });

        test('search is case insensitive', function () {
            $searchTerms = ['iphone', 'IPHONE', 'iPhone', 'IpHoNe'];
            
            foreach ($searchTerms as $term) {
                $response = $this->get("/auctions?search={$term}");
                $auctions = $response->json('props.auctions.data');
                expect(count($auctions))->toBeGreaterThan(0);
            }
        });

        test('search handles special characters', function () {
            $specialTerms = [
                'iPhone+13',
                'MacBook Pro',
                'S22-Ultra',
            ];
            
            foreach ($specialTerms as $term) {
                $response = $this->get('/auctions?search=' . urlencode($term));
                expect($response->status())->toBe(200);
            }
        });

        test('search returns empty results for non-existent terms', function () {
            $response = $this->get('/auctions?search=nonexistentproduct12345');
            assertInertiaResponse($response, 'Auctions/Index');
            
            $auctions = $response->json('props.auctions.data');
            expect(count($auctions))->toBe(0);
        });

        test('empty search returns all results', function () {
            $allResponse = $this->get('/auctions');
            $emptySearchResponse = $this->get('/auctions?search=');
            
            $allCount = count($allResponse->json('props.auctions.data'));
            $searchCount = count($emptySearchResponse->json('props.auctions.data'));
            
            expect($searchCount)->toBe($allCount);
        });
    });

    describe('Advanced Search Features', function () {
        test('search supports multiple keywords', function () {
            $response = $this->get('/auctions?search=Apple+smartphone');
            assertInertiaResponse($response, 'Auctions/Index');
            
            $auctions = $response->json('props.auctions.data');
            expect(count($auctions))->toBeGreaterThan(0);
        });

        test('search can be combined with filters', function () {
            $response = $this->get("/auctions?search=phone&category={$this->category->id}");
            assertInertiaResponse($response, 'Auctions/Index');
            
            $auctions = $response->json('props.auctions.data');
            foreach ($auctions as $auction) {
                expect($auction['category']['id'])->toBe($this->category->id);
            }
        });

        test('search can be combined with sorting', function () {
            $response = $this->get('/auctions?search=phone&sort=starting_bid&direction=desc');
            assertInertiaResponse($response, 'Auctions/Index');
            
            $auctions = $response->json('props.auctions.data');
            if (count($auctions) > 1) {
                expect($auctions[0]['starting_bid'])->toBeGreaterThanOrEqual($auctions[1]['starting_bid']);
            }
        });

        test('search supports pagination', function () {
            // Create more searchable auctions
            for ($i = 0; $i < 20; $i++) {
                createActiveAuction([
                    'title' => "Phone Model {$i}",
                    'category_id' => $this->category->id,
                ]);
            }

            $response = $this->get('/auctions?search=phone&page=1');
            assertInertiaResponse($response, 'Auctions/Index');
            
            $props = $response->json('props');
            expect($props['auctions'])->toHaveKey('current_page');
            expect($props['auctions'])->toHaveKey('last_page');
        });

        test('search highlights or indicates search terms', function () {
            $response = $this->get('/auctions?search=iPhone');
            assertInertiaResponse($response, 'Auctions/Index');
            
            $props = $response->json('props');
            expect($props)->toHaveKey('search_query');
            expect($props['search_query'])->toBe('iPhone');
        });
    });

    describe('Category Search', function () {
        test('categories can be searched by name', function () {
            $response = $this->get('/categories?search=Electronics');
            
            if ($response->status() === 200) {
                $categories = $response->json('props.categories');
                expect(count($categories))->toBeGreaterThan(0);
                
                foreach ($categories as $category) {
                    expect(strtolower($category['name']))->toContain('electronics');
                }
            }
        });

        test('category search includes description', function () {
            $categoryWithDescription = createTestCategory([
                'name' => 'Books',
                'description' => 'Literature and educational materials',
            ]);

            $response = $this->get('/categories?search=literature');
            
            if ($response->status() === 200) {
                $categories = $response->json('props.categories');
                $found = false;
                foreach ($categories as $category) {
                    if ($category['id'] === $categoryWithDescription->id) {
                        $found = true;
                        break;
                    }
                }
                expect($found)->toBe(true);
            }
        });
    });

    describe('Admin Search Features', function () {
        test('admin can search users by email', function () {
            $testUser = createVerifiedUser(['email' => '<EMAIL>']);

            $response = $this->actingAs($this->admin)->get('/admin/users?search=searchtest');
            
            if ($response->status() === 200) {
                $users = $response->json('props.users.data');
                $found = false;
                foreach ($users as $user) {
                    if ($user['email'] === '<EMAIL>') {
                        $found = true;
                        break;
                    }
                }
                expect($found)->toBe(true);
            }
        });

        test('admin can search users by name', function () {
            $testUser = createVerifiedUser(['name' => 'John SearchTest']);

            $response = $this->actingAs($this->admin)->get('/admin/users?search=SearchTest');
            
            if ($response->status() === 200) {
                $users = $response->json('props.users.data');
                $found = false;
                foreach ($users as $user) {
                    if (stripos($user['name'], 'SearchTest') !== false) {
                        $found = true;
                        break;
                    }
                }
                expect($found)->toBe(true);
            }
        });

        test('admin can search auctions by title and description', function () {
            $response = $this->actingAs($this->admin)->get('/admin/auctions?search=iPhone');
            
            if ($response->status() === 200) {
                $auctions = $response->json('props.auctions.data');
                expect(count($auctions))->toBeGreaterThan(0);
            }
        });

        test('regular user cannot access admin search', function () {
            $response = $this->actingAs($this->user)->get('/admin/users?search=test');
            assertPageRequiresAdmin($response);
        });
    });

    describe('Search Performance and Security', function () {
        test('search queries execute within acceptable time', function () {
            assertExecutionTimeUnder(function() {
                $this->get('/auctions?search=phone');
            }, 2.0);
        });

        test('search handles SQL injection attempts', function () {
            $maliciousQueries = [
                "'; DROP TABLE auctions; --",
                "' OR '1'='1",
                "' UNION SELECT * FROM users --",
            ];

            foreach ($maliciousQueries as $query) {
                $response = $this->get('/auctions?search=' . urlencode($query));
                expect($response->status())->toBe(200);
                // Should not cause database errors
            }
        });

        test('search prevents XSS in search terms', function () {
            $xssQuery = '<script>alert("xss")</script>';
            
            $response = $this->get('/auctions?search=' . urlencode($xssQuery));
            assertInertiaResponse($response, 'Auctions/Index');
            
            $searchQuery = $response->json('props.search_query');
            expect($searchQuery)->not->toContain('<script>');
        });

        test('search is rate limited for excessive requests', function () {
            // Perform many search requests rapidly
            for ($i = 0; $i < 100; $i++) {
                $response = $this->get("/auctions?search=query{$i}");
                if ($response->status() === 429) {
                    break;
                }
            }

            // Should eventually hit rate limit
            expect($response->status())->toBeIn([200, 429]);
        });

        test('search queries are optimized for large datasets', function () {
            // Create many auctions
            for ($i = 0; $i < 100; $i++) {
                createActiveAuction([
                    'title' => "Product {$i}",
                    'category_id' => $this->category->id,
                ]);
            }

            testDatabaseQueryPerformance(function() {
                $this->get('/auctions?search=Product');
            }, 15); // Should not require excessive queries
        });
    });

    describe('Search Analytics and Tracking', function () {
        test('search terms are logged for analytics', function () {
            // This would test search analytics if implemented
            $this->markTestSkipped('Search analytics testing requires specific implementation');
        });

        test('popular search terms are tracked', function () {
            // This would test popular search tracking if implemented
            $this->markTestSkipped('Popular search tracking requires specific implementation');
        });

        test('no results searches are tracked', function () {
            // This would test no-results tracking if implemented
            $this->markTestSkipped('No results tracking requires specific implementation');
        });
    });

    describe('Search Suggestions and Autocomplete', function () {
        test('search provides suggestions for partial matches', function () {
            // This would test search suggestions if implemented
            $this->markTestSkipped('Search suggestions testing requires specific implementation');
        });

        test('search autocomplete works correctly', function () {
            // This would test autocomplete functionality if implemented
            $this->markTestSkipped('Search autocomplete testing requires specific implementation');
        });

        test('search corrects common misspellings', function () {
            // This would test spell correction if implemented
            $this->markTestSkipped('Spell correction testing requires specific implementation');
        });
    });
});
