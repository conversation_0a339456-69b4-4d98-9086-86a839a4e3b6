<?php

declare(strict_types=1);

use App\Models\Category;
use App\Models\User;

describe('Category CRUD Operations Tests', function () {
    beforeEach(function () {
        $this->admin = createAdminUser();
        $this->user = createVerifiedUser();
        $this->category = createTestCategory([
            'name' => 'Electronics',
            'slug' => 'electronics',
            'description' => 'Electronic devices and gadgets',
        ]);
    });

    describe('Category Creation (Create)', function () {
        test('admin can create new category', function () {
            $categoryData = [
                'name' => 'Books & Literature',
                'description' => 'Books, magazines, and literary works',
                'is_active' => true,
            ];

            $response = $this->actingAs($this->admin)->post('/admin/categories', $categoryData);

            expect($response->status())->toBeIn([201, 302]);
            assertDatabaseHasRecord('categories', [
                'name' => 'Books & Literature',
                'slug' => 'books-literature',
            ]);
        });

        test('regular user cannot create categories', function () {
            $categoryData = [
                'name' => 'Unauthorized Category',
                'description' => 'This should not be created',
            ];

            $response = $this->actingAs($this->user)->post('/admin/categories', $categoryData);

            assertPageRequiresAdmin($response);
            assertDatabaseMissingRecord('categories', [
                'name' => 'Unauthorized Category',
            ]);
        });

        test('guest cannot create categories', function () {
            $categoryData = [
                'name' => 'Guest Category',
                'description' => 'This should not be created',
            ];

            $response = $this->post('/admin/categories', $categoryData);

            assertPageRequiresAuth($response);
        });

        test('category creation validates required fields', function () {
            $invalidData = [
                'name' => '',
                'description' => '',
            ];

            $response = $this->actingAs($this->admin)->post('/admin/categories', $invalidData);

            assertValidationErrors($response, ['name', 'description']);
        });

        test('category creation validates unique name', function () {
            $duplicateData = [
                'name' => $this->category->name,
                'description' => 'Duplicate category name',
            ];

            $response = $this->actingAs($this->admin)->post('/admin/categories', $duplicateData);

            assertValidationErrors($response, ['name']);
        });

        test('category creation auto-generates slug', function () {
            $categoryData = [
                'name' => 'Art & Collectibles',
                'description' => 'Artwork and collectible items',
            ];

            $this->actingAs($this->admin)->post('/admin/categories', $categoryData);

            assertDatabaseHasRecord('categories', [
                'name' => 'Art & Collectibles',
                'slug' => 'art-collectibles',
            ]);
        });

        test('category creation handles special characters in name', function () {
            $categoryData = [
                'name' => 'Toys & Games (Kids)',
                'description' => 'Children toys and games',
            ];

            $this->actingAs($this->admin)->post('/admin/categories', $categoryData);

            $category = Category::where('name', 'Toys & Games (Kids)')->first();
            expect($category->slug)->toMatch('/^[a-z0-9-]+$/'); // Should be URL-safe
        });
    });

    describe('Category Reading (Read)', function () {
        test('anyone can view category listing', function () {
            $response = $this->get('/categories');
            assertInertiaResponse($response, 'Categories/Index');
            
            $props = $response->json('props');
            expect($props['categories'])->toBeArray();
        });

        test('anyone can view individual category', function () {
            $response = $this->get("/categories/{$this->category->slug}");
            assertInertiaResponse($response, 'Categories/Show');
            
            $props = $response->json('props');
            expect($props['category']['id'])->toBe($this->category->id);
            expect($props['category']['name'])->toBe($this->category->name);
        });

        test('category shows associated auctions', function () {
            // Create auctions in this category
            createActiveAuction(['category_id' => $this->category->id]);
            createActiveAuction(['category_id' => $this->category->id]);

            $response = $this->get("/categories/{$this->category->slug}/auctions");
            assertInertiaResponse($response, 'Categories/Auctions');
            
            $props = $response->json('props');
            expect($props['auctions']['data'])->toBeArray();
            expect(count($props['auctions']['data']))->toBeGreaterThanOrEqual(2);
        });

        test('admin can view category management page', function () {
            $response = $this->actingAs($this->admin)->get('/admin/categories');
            
            if ($response->status() === 200) {
                assertInertiaResponse($response, 'Admin/Categories/Index');
                expect($response->json('props.categories'))->toBeArray();
            }
        });

        test('category listing supports search', function () {
            $response = $this->get("/categories?search={$this->category->name}");
            
            if ($response->status() === 200) {
                $categories = $response->json('props.categories');
                expect($categories[0]['name'])->toContain($this->category->name);
            }
        });

        test('inactive categories are hidden from public', function () {
            $this->category->update(['is_active' => false]);

            $response = $this->get('/categories');
            $categories = $response->json('props.categories');
            
            $categoryIds = array_column($categories, 'id');
            expect($categoryIds)->not->toContain($this->category->id);
        });

        test('non-existent category returns 404', function () {
            $response = $this->get('/categories/non-existent-category');
            expect($response->status())->toBe(404);
        });
    });

    describe('Category Updating (Update)', function () {
        test('admin can update category', function () {
            $updateData = [
                'name' => 'Updated Electronics',
                'description' => 'Updated description for electronics',
                'is_active' => false,
            ];

            $response = $this->actingAs($this->admin)->put("/admin/categories/{$this->category->id}", $updateData);

            expect($response->status())->toBeIn([200, 302]);
            assertDatabaseHasRecord('categories', [
                'id' => $this->category->id,
                'name' => 'Updated Electronics',
                'is_active' => false,
            ]);
        });

        test('regular user cannot update categories', function () {
            $updateData = ['name' => 'Unauthorized Update'];

            $response = $this->actingAs($this->user)->put("/admin/categories/{$this->category->id}", $updateData);

            assertPageRequiresAdmin($response);
            assertDatabaseMissingRecord('categories', [
                'id' => $this->category->id,
                'name' => 'Unauthorized Update',
            ]);
        });

        test('category update validates required fields', function () {
            $invalidData = [
                'name' => '',
                'description' => '',
            ];

            $response = $this->actingAs($this->admin)->put("/admin/categories/{$this->category->id}", $invalidData);

            assertValidationErrors($response, ['name', 'description']);
        });

        test('category update validates unique name', function () {
            $otherCategory = createTestCategory(['name' => 'Other Category']);
            
            $updateData = ['name' => $otherCategory->name];

            $response = $this->actingAs($this->admin)->put("/admin/categories/{$this->category->id}", $updateData);

            assertValidationErrors($response, ['name']);
        });

        test('category update regenerates slug when name changes', function () {
            $updateData = ['name' => 'Completely New Name'];

            $this->actingAs($this->admin)->put("/admin/categories/{$this->category->id}", $updateData);

            $this->category->refresh();
            expect($this->category->slug)->toBe('completely-new-name');
        });

        test('updating category preserves existing auctions', function () {
            $auction = createActiveAuction(['category_id' => $this->category->id]);

            $updateData = ['name' => 'Updated Category Name'];
            $this->actingAs($this->admin)->put("/admin/categories/{$this->category->id}", $updateData);

            $auction->refresh();
            expect($auction->category_id)->toBe($this->category->id);
        });
    });

    describe('Category Deletion (Delete)', function () {
        test('admin can delete category without auctions', function () {
            $response = $this->actingAs($this->admin)->delete("/admin/categories/{$this->category->id}");

            expect($response->status())->toBeIn([200, 302]);
            assertDatabaseMissingRecord('categories', ['id' => $this->category->id]);
        });

        test('regular user cannot delete categories', function () {
            $response = $this->actingAs($this->user)->delete("/admin/categories/{$this->category->id}");

            assertPageRequiresAdmin($response);
            assertDatabaseHasRecord('categories', ['id' => $this->category->id]);
        });

        test('cannot delete category with active auctions', function () {
            createActiveAuction(['category_id' => $this->category->id]);

            $response = $this->actingAs($this->admin)->delete("/admin/categories/{$this->category->id}");

            expect($response->status())->toBeIn([422, 403]);
            assertDatabaseHasRecord('categories', ['id' => $this->category->id]);
        });

        test('can delete category after moving auctions', function () {
            $auction = createActiveAuction(['category_id' => $this->category->id]);
            $newCategory = createTestCategory(['name' => 'New Category']);

            // Move auction to new category
            $auction->update(['category_id' => $newCategory->id]);

            $response = $this->actingAs($this->admin)->delete("/admin/categories/{$this->category->id}");

            expect($response->status())->toBeIn([200, 302]);
            assertDatabaseMissingRecord('categories', ['id' => $this->category->id]);
        });

        test('soft delete preserves category data', function () {
            // If using soft deletes
            $this->actingAs($this->admin)->delete("/admin/categories/{$this->category->id}");

            // Category should still exist in database but marked as deleted
            $category = Category::withTrashed()->find($this->category->id);
            if ($category) {
                expect($category->deleted_at)->not->toBeNull();
            }
        });
    });

    describe('Category Business Logic', function () {
        test('category auction count is accurate', function () {
            // Create multiple auctions in category
            createActiveAuction(['category_id' => $this->category->id]);
            createActiveAuction(['category_id' => $this->category->id]);
            createEndedAuction(['category_id' => $this->category->id]);

            $response = $this->get("/categories/{$this->category->slug}");
            
            if ($response->status() === 200) {
                $category = $response->json('props.category');
                expect($category['auctions_count'])->toBe(3);
            }
        });

        test('category ordering works correctly', function () {
            $category1 = createTestCategory(['name' => 'A Category', 'sort_order' => 1]);
            $category2 = createTestCategory(['name' => 'B Category', 'sort_order' => 2]);

            $response = $this->get('/categories');
            $categories = $response->json('props.categories');

            // Should be ordered by sort_order or name
            expect($categories[0]['name'])->toBe('A Category');
        });

        test('category statistics are calculated correctly', function () {
            // Create auctions with different statuses
            createActiveAuction(['category_id' => $this->category->id]);
            createEndedAuction(['category_id' => $this->category->id]);

            $response = $this->actingAs($this->admin)->get("/admin/categories/{$this->category->id}");
            
            if ($response->status() === 200) {
                $stats = $response->json('props.statistics');
                expect($stats)->toHaveKey('total_auctions');
                expect($stats)->toHaveKey('active_auctions');
            }
        });

        test('category slug conflicts are handled', function () {
            $categoryData = [
                'name' => 'Electronics', // Same as existing category
                'description' => 'Another electronics category',
            ];

            $response = $this->actingAs($this->admin)->post('/admin/categories', $categoryData);

            // Should either fail validation or generate unique slug
            if ($response->status() === 201 || $response->status() === 302) {
                $newCategory = Category::where('name', 'Electronics')->where('id', '!=', $this->category->id)->first();
                expect($newCategory->slug)->not->toBe($this->category->slug);
            } else {
                assertValidationErrors($response, ['name']);
            }
        });
    });
});
