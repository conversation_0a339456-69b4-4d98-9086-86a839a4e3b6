<?php

declare(strict_types=1);

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;

describe('Data Tables and Empty States Tests', function () {
    beforeEach(function () {
        $this->user = createVerifiedUser();
        $this->admin = createAdminUser();
        $this->category = createTestCategory();
    });

    describe('Data Table Structure', function () {
        test('auction table displays required columns', function () {
            createActiveAuction(['category_id' => $this->category->id]);

            $response = $this->get('/auctions');
            assertInertiaResponse($response, 'Auctions/Index');
            
            $auctions = $response->json('props.auctions.data');
            foreach ($auctions as $auction) {
                expect($auction)->toHaveKey('id');
                expect($auction)->toHaveKey('title');
                expect($auction)->toHaveKey('starting_bid');
                expect($auction)->toHaveKey('current_bid');
                expect($auction)->toHaveKey('end_time');
                expect($auction)->toHaveKey('status');
                expect($auction)->toHaveKey('category');
                expect($auction)->toHave<PERSON>ey('user');
            }
        });

        test('admin user table displays management columns', function () {
            $response = $this->actingAs($this->admin)->get('/admin/users');
            
            if ($response->status() === 200) {
                $users = $response->json('props.users.data');
                foreach ($users as $user) {
                    expect($user)->toHaveKey('id');
                    expect($user)->toHaveKey('name');
                    expect($user)->toHaveKey('email');
                    expect($user)->toHaveKey('is_active');
                    expect($user)->toHaveKey('email_verified_at');
                    expect($user)->toHaveKey('created_at');
                }
            }
        });

        test('admin auction table displays management columns', function () {
            createActiveAuction(['category_id' => $this->category->id]);

            $response = $this->actingAs($this->admin)->get('/admin/auctions');
            
            if ($response->status() === 200) {
                $auctions = $response->json('props.auctions.data');
                foreach ($auctions as $auction) {
                    expect($auction)->toHaveKey('id');
                    expect($auction)->toHaveKey('title');
                    expect($auction)->toHaveKey('status');
                    expect($auction)->toHaveKey('user');
                    expect($auction)->toHaveKey('category');
                    expect($auction)->toHaveKey('created_at');
                }
            }
        });

        test('bid table displays required information', function () {
            $auction = createActiveAuction(['category_id' => $this->category->id]);
            createTestBid(['auction_id' => $auction->id, 'user_id' => $this->user->id]);

            $response = $this->get("/auctions/{$auction->id}/bids");
            
            if ($response->status() === 200) {
                $bids = $response->json('data') ?? $response->json();
                foreach ($bids as $bid) {
                    expect($bid)->toHaveKey('id');
                    expect($bid)->toHaveKey('amount');
                    expect($bid)->toHaveKey('created_at');
                    expect($bid)->toHaveKey('user');
                }
            }
        });
    });

    describe('Data Table Sorting', function () {
        beforeEach(function () {
            // Create test data with different values for sorting
            createActiveAuction([
                'title' => 'A First Auction',
                'starting_bid' => 100,
                'category_id' => $this->category->id,
            ]);
            createActiveAuction([
                'title' => 'Z Last Auction',
                'starting_bid' => 50,
                'category_id' => $this->category->id,
            ]);
        });

        test('auction table sorts by title', function () {
            $ascResponse = $this->get('/auctions?sort=title&direction=asc');
            $descResponse = $this->get('/auctions?sort=title&direction=desc');
            
            $ascAuctions = $ascResponse->json('props.auctions.data');
            $descAuctions = $descResponse->json('props.auctions.data');
            
            if (count($ascAuctions) > 1) {
                expect($ascAuctions[0]['title'])->toBeLessThanOrEqual($ascAuctions[1]['title']);
            }
            if (count($descAuctions) > 1) {
                expect($descAuctions[0]['title'])->toBeGreaterThanOrEqual($descAuctions[1]['title']);
            }
        });

        test('auction table sorts by price', function () {
            $ascResponse = $this->get('/auctions?sort=starting_bid&direction=asc');
            $descResponse = $this->get('/auctions?sort=starting_bid&direction=desc');
            
            $ascAuctions = $ascResponse->json('props.auctions.data');
            $descAuctions = $descResponse->json('props.auctions.data');
            
            if (count($ascAuctions) > 1) {
                expect($ascAuctions[0]['starting_bid'])->toBeLessThanOrEqual($ascAuctions[1]['starting_bid']);
            }
            if (count($descAuctions) > 1) {
                expect($descAuctions[0]['starting_bid'])->toBeGreaterThanOrEqual($descAuctions[1]['starting_bid']);
            }
        });

        test('admin tables support sorting', function () {
            $response = $this->actingAs($this->admin)->get('/admin/users?sort=created_at&direction=desc');
            
            if ($response->status() === 200) {
                $users = $response->json('props.users.data');
                expect(count($users))->toBeGreaterThan(0);
            }
        });

        test('invalid sort parameters are handled gracefully', function () {
            $response = $this->get('/auctions?sort=invalid_field&direction=invalid');
            expect($response->status())->toBe(200);
            
            $auctions = $response->json('props.auctions.data');
            expect($auctions)->toBeArray();
        });
    });

    describe('Data Table Actions', function () {
        test('auction table shows appropriate actions for owner', function () {
            $auction = createActiveAuction([
                'user_id' => $this->user->id,
                'category_id' => $this->category->id,
            ]);

            $response = $this->actingAs($this->user)->get('/dashboard');
            
            if ($response->status() === 200) {
                $props = $response->json('props');
                // Should include user's auctions with edit/delete actions
                expect($props)->toHaveKey('user_auctions');
            }
        });

        test('admin tables show management actions', function () {
            $response = $this->actingAs($this->admin)->get('/admin/users');
            
            if ($response->status() === 200) {
                $users = $response->json('props.users.data');
                foreach ($users as $user) {
                    // Admin should see user management options
                    expect($user)->toHaveKey('is_active');
                }
            }
        });

        test('bulk actions are available for admin', function () {
            $response = $this->actingAs($this->admin)->get('/admin/auctions');
            
            if ($response->status() === 200) {
                $props = $response->json('props');
                // Should include bulk action capabilities
                expect($props)->toBeArray();
            }
        });

        test('regular user cannot see admin actions', function () {
            $response = $this->actingAs($this->user)->get('/auctions');
            $auctions = $response->json('props.auctions.data');
            
            foreach ($auctions as $auction) {
                // Regular users shouldn't see admin-only fields
                expect($auction)->not->toHaveKey('admin_actions');
            }
        });
    });

    describe('Empty States', function () {
        test('auction list shows empty state when no auctions exist', function () {
            // Clear all auctions
            Auction::query()->delete();

            $response = $this->get('/auctions');
            assertInertiaResponse($response, 'Auctions/Index');
            
            $auctions = $response->json('props.auctions.data');
            expect($auctions)->toBeEmpty();
            
            // Should provide empty state information
            $props = $response->json('props');
            expect($props['auctions']['total'])->toBe(0);
        });

        test('category shows empty state when no auctions exist', function () {
            $emptyCategory = createTestCategory(['name' => 'Empty Category']);

            $response = $this->get("/categories/{$emptyCategory->slug}/auctions");
            assertInertiaResponse($response, 'Categories/Auctions');
            
            $auctions = $response->json('props.auctions.data');
            expect($auctions)->toBeEmpty();
        });

        test('search shows empty state for no results', function () {
            $response = $this->get('/auctions?search=nonexistentitem12345');
            assertInertiaResponse($response, 'Auctions/Index');
            
            $auctions = $response->json('props.auctions.data');
            expect($auctions)->toBeEmpty();
            
            $props = $response->json('props');
            expect($props['search_query'])->toBe('nonexistentitem12345');
        });

        test('user dashboard shows empty state for new users', function () {
            $newUser = createVerifiedUser();

            $response = $this->actingAs($newUser)->get('/dashboard');
            assertInertiaResponse($response, 'Dashboard');
            
            $props = $response->json('props');
            // New user should have empty states for auctions, bids, etc.
            expect($props)->toHaveKey('user_auctions');
            expect($props)->toHaveKey('user_bids');
        });

        test('bid list shows empty state when no bids exist', function () {
            $auction = createActiveAuction(['category_id' => $this->category->id]);

            $response = $this->get("/auctions/{$auction->id}/bids");
            
            if ($response->status() === 200) {
                $bids = $response->json('data') ?? $response->json();
                expect($bids)->toBeEmpty();
            }
        });

        test('admin lists show empty states appropriately', function () {
            // Test with minimal data
            $response = $this->actingAs($this->admin)->get('/admin/users?status=suspended');
            
            if ($response->status() === 200) {
                $users = $response->json('props.users.data');
                // Might be empty if no suspended users exist
                expect($users)->toBeArray();
            }
        });
    });

    describe('Data Table Responsiveness', function () {
        test('tables adapt to different screen sizes', function () {
            // This would test responsive table behavior
            $this->markTestSkipped('Responsive table testing requires frontend testing tools');
        });

        test('tables provide mobile-friendly views', function () {
            // This would test mobile table layouts
            $this->markTestSkipped('Mobile table testing requires frontend testing tools');
        });

        test('tables handle overflow gracefully', function () {
            // Create auction with very long title
            $longTitleAuction = createActiveAuction([
                'title' => str_repeat('Very Long Auction Title ', 10),
                'category_id' => $this->category->id,
            ]);

            $response = $this->get('/auctions');
            assertInertiaResponse($response, 'Auctions/Index');
            
            $auctions = $response->json('props.auctions.data');
            expect(count($auctions))->toBeGreaterThan(0);
        });
    });

    describe('Data Table Performance', function () {
        test('tables load efficiently with large datasets', function () {
            // Create many records
            for ($i = 0; $i < 50; $i++) {
                createActiveAuction(['category_id' => $this->category->id]);
            }

            assertExecutionTimeUnder(function() {
                $this->get('/auctions');
            }, 3.0);
        });

        test('table sorting is performant', function () {
            // Create many records
            for ($i = 0; $i < 30; $i++) {
                createActiveAuction(['category_id' => $this->category->id]);
            }

            assertExecutionTimeUnder(function() {
                $this->get('/auctions?sort=starting_bid&direction=desc');
            }, 3.0);
        });

        test('table pagination is optimized', function () {
            testDatabaseQueryPerformance(function() {
                $this->get('/auctions?page=2');
            }, 15); // Should not require excessive queries
        });

        test('admin tables handle large user bases', function () {
            // Create many users
            for ($i = 0; $i < 25; $i++) {
                createVerifiedUser(['email' => "user{$i}@example.com"]);
            }

            assertExecutionTimeUnder(function() {
                $this->actingAs($this->admin)->get('/admin/users');
            }, 3.0);
        });
    });

    describe('Data Table Accessibility', function () {
        test('tables include proper ARIA labels', function () {
            // This would test accessibility features
            $this->markTestSkipped('Accessibility testing requires frontend testing tools');
        });

        test('tables support keyboard navigation', function () {
            // This would test keyboard accessibility
            $this->markTestSkipped('Keyboard navigation testing requires frontend testing tools');
        });

        test('tables work with screen readers', function () {
            // This would test screen reader compatibility
            $this->markTestSkipped('Screen reader testing requires specialized tools');
        });
    });
});
