<?php

declare(strict_types=1);

use App\Models\User;

describe('User CRUD Operations Tests', function () {
    beforeEach(function () {
        $this->user = createVerifiedUser();
        $this->admin = createAdminUser();
        $this->otherUser = createVerifiedUser(['email' => '<EMAIL>']);
    });

    describe('User Profile Reading (Read)', function () {
        test('user can view their own profile', function () {
            $response = $this->actingAs($this->user)->get('/settings/profile');
            assertInertiaResponse($response, 'Settings/Profile');
            
            $props = $response->json('props');
            expect($props['user']['id'])->toBe($this->user->id);
            expect($props['user']['email'])->toBe($this->user->email);
        });

        test('user cannot view other users profiles directly', function () {
            $response = $this->actingAs($this->user)->get("/users/{$this->otherUser->id}");
            expect($response->status())->toBeIn([403, 404]);
        });

        test('admin can view any user profile', function () {
            $response = $this->actingAs($this->admin)->get("/admin/users/{$this->user->id}");
            
            if ($response->status() === 200) {
                assertInertiaResponse($response, 'Admin/Users/<USER>');
                expect($response->json('props.user.id'))->toBe($this->user->id);
            }
        });

        test('guest cannot view user profiles', function () {
            $response = $this->get('/settings/profile');
            assertPageRequiresAuth($response);
        });

        test('user listing is paginated for admin', function () {
            // Create multiple users
            for ($i = 0; $i < 25; $i++) {
                createVerifiedUser(['email' => "user{$i}@example.com"]);
            }

            $response = $this->actingAs($this->admin)->get('/admin/users');
            
            if ($response->status() === 200) {
                $props = $response->json('props');
                expect($props['users'])->toHaveKey('current_page');
                expect($props['users'])->toHaveKey('last_page');
            }
        });
    });

    describe('User Profile Updating (Update)', function () {
        test('user can update their own profile', function () {
            $updateData = [
                'name' => 'Updated Name',
                'phone' => '+1234567890',
                'address' => '123 Updated St',
                'city' => 'Updated City',
                'state' => 'Updated State',
                'zip_code' => '12345',
            ];

            $response = $this->actingAs($this->user)->patch('/settings/profile', $updateData);

            expect($response->status())->toBeIn([200, 302]);
            assertDatabaseHasRecord('users', [
                'id' => $this->user->id,
                'name' => 'Updated Name',
                'phone' => '+1234567890',
            ]);
        });

        test('user cannot update other users profiles', function () {
            $updateData = ['name' => 'Malicious Update'];

            $response = $this->actingAs($this->user)->patch("/users/{$this->otherUser->id}", $updateData);

            expect($response->status())->toBeIn([403, 404, 405]);
            assertDatabaseMissingRecord('users', [
                'id' => $this->otherUser->id,
                'name' => 'Malicious Update',
            ]);
        });

        test('admin can update any user profile', function () {
            $updateData = [
                'name' => 'Admin Updated Name',
                'is_active' => false,
            ];

            $response = $this->actingAs($this->admin)->put("/admin/users/{$this->user->id}", $updateData);

            expect($response->status())->toBeIn([200, 302]);
            assertDatabaseHasRecord('users', [
                'id' => $this->user->id,
                'name' => 'Admin Updated Name',
            ]);
        });

        test('profile update validates required fields', function () {
            $invalidData = [
                'name' => '',
                'email' => '',
            ];

            $response = $this->actingAs($this->user)->patch('/settings/profile', $invalidData);

            assertValidationErrors($response, ['name', 'email']);
        });

        test('profile update validates email format', function () {
            $invalidData = [
                'email' => 'invalid-email',
            ];

            $response = $this->actingAs($this->user)->patch('/settings/profile', $invalidData);

            assertValidationErrors($response, ['email']);
        });

        test('profile update validates email uniqueness', function () {
            $invalidData = [
                'email' => $this->otherUser->email,
            ];

            $response = $this->actingAs($this->user)->patch('/settings/profile', $invalidData);

            assertValidationErrors($response, ['email']);
        });

        test('profile update sanitizes input data', function () {
            $maliciousData = [
                'name' => '<script>alert("xss")</script>John Doe',
                'address' => '<img src=x onerror=alert(1)>123 Main St',
            ];

            $response = $this->actingAs($this->user)->patch('/settings/profile', $maliciousData);

            $this->user->refresh();
            expect($this->user->name)->not->toContain('<script>');
            expect($this->user->address)->not->toContain('<img');
        });
    });

    describe('Password Management', function () {
        test('user can update their password', function () {
            $passwordData = [
                'current_password' => 'password',
                'password' => 'newpassword123',
                'password_confirmation' => 'newpassword123',
            ];

            $response = $this->actingAs($this->user)->put('/settings/password', $passwordData);

            expect($response->status())->toBeIn([200, 302]);
            
            // Verify new password works
            $this->post('/logout');
            $loginResponse = $this->post('/login', [
                'email' => $this->user->email,
                'password' => 'newpassword123',
            ]);
            $this->assertAuthenticated();
        });

        test('password update requires current password', function () {
            $passwordData = [
                'current_password' => 'wrongpassword',
                'password' => 'newpassword123',
                'password_confirmation' => 'newpassword123',
            ];

            $response = $this->actingAs($this->user)->put('/settings/password', $passwordData);

            assertValidationErrors($response, ['current_password']);
        });

        test('password update requires confirmation', function () {
            $passwordData = [
                'current_password' => 'password',
                'password' => 'newpassword123',
                'password_confirmation' => 'differentpassword',
            ];

            $response = $this->actingAs($this->user)->put('/settings/password', $passwordData);

            assertValidationErrors($response, ['password']);
        });

        test('password update validates minimum length', function () {
            $passwordData = [
                'current_password' => 'password',
                'password' => '123',
                'password_confirmation' => '123',
            ];

            $response = $this->actingAs($this->user)->put('/settings/password', $passwordData);

            assertValidationErrors($response, ['password']);
        });
    });

    describe('User Account Deletion', function () {
        test('user can delete their own account', function () {
            $response = $this->actingAs($this->user)->delete('/settings/profile', [
                'password' => 'password',
            ]);

            expect($response->status())->toBeIn([200, 302]);
            
            // User should be soft deleted or marked inactive
            $this->user->refresh();
            expect($this->user->is_active)->toBe(false);
        });

        test('account deletion requires password confirmation', function () {
            $response = $this->actingAs($this->user)->delete('/settings/profile', [
                'password' => 'wrongpassword',
            ]);

            assertValidationErrors($response, ['password']);
        });

        test('admin can deactivate user accounts', function () {
            $response = $this->actingAs($this->admin)->post("/admin/users/{$this->user->id}/deactivate");

            expect($response->status())->toBeIn([200, 302]);
            assertDatabaseHasRecord('users', [
                'id' => $this->user->id,
                'is_active' => false,
            ]);
        });

        test('admin can reactivate user accounts', function () {
            $this->user->update(['is_active' => false]);

            $response = $this->actingAs($this->admin)->post("/admin/users/{$this->user->id}/activate");

            expect($response->status())->toBeIn([200, 302]);
            assertDatabaseHasRecord('users', [
                'id' => $this->user->id,
                'is_active' => true,
            ]);
        });

        test('deleting user handles associated data', function () {
            // Create user with auctions and bids
            $auction = createActiveAuction(['user_id' => $this->user->id]);
            $bid = createTestBid(['user_id' => $this->user->id]);

            $this->actingAs($this->user)->delete('/settings/profile', [
                'password' => 'password',
            ]);

            // Verify associated data is handled appropriately
            // (soft delete, transfer ownership, etc.)
            $this->user->refresh();
            expect($this->user->is_active)->toBe(false);
        });
    });

    describe('User Security and Privacy', function () {
        test('user data is properly encrypted', function () {
            // This would test that sensitive data is encrypted at rest
            $this->markTestSkipped('Data encryption testing requires specific implementation');
        });

        test('user sessions are invalidated on password change', function () {
            // Login and get session
            $this->actingAs($this->user);
            $sessionId = session()->getId();

            // Change password
            $this->put('/settings/password', [
                'current_password' => 'password',
                'password' => 'newpassword123',
                'password_confirmation' => 'newpassword123',
            ]);

            // Session should be regenerated
            expect(session()->getId())->not->toBe($sessionId);
        });

        test('user cannot escalate privileges', function () {
            $maliciousData = [
                'is_admin' => true,
                'is_active' => true,
                'email_verified_at' => now(),
            ];

            $response = $this->actingAs($this->user)->patch('/settings/profile', $maliciousData);

            $this->user->refresh();
            expect($this->user->is_admin)->toBe(false);
        });

        test('user profile updates are logged', function () {
            // This would test audit logging if implemented
            $this->markTestSkipped('Audit logging testing requires specific implementation');
        });
    });

    describe('User Search and Filtering', function () {
        test('admin can search users by email', function () {
            $response = $this->actingAs($this->admin)->get("/admin/users?search={$this->user->email}");
            
            if ($response->status() === 200) {
                $users = $response->json('props.users.data');
                expect($users[0]['email'])->toBe($this->user->email);
            }
        });

        test('admin can filter users by status', function () {
            $inactiveUser = createInactiveUser();

            $response = $this->actingAs($this->admin)->get('/admin/users?status=inactive');
            
            if ($response->status() === 200) {
                $users = $response->json('props.users.data');
                foreach ($users as $user) {
                    expect($user['is_active'])->toBe(false);
                }
            }
        });

        test('admin can sort users by registration date', function () {
            $response = $this->actingAs($this->admin)->get('/admin/users?sort=created_at&direction=desc');
            
            if ($response->status() === 200) {
                $users = $response->json('props.users.data');
                expect($users)->toBeArray();
                // Verify sorting if multiple users exist
            }
        });
    });
});
